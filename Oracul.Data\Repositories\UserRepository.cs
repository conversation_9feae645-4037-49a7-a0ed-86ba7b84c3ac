using Microsoft.EntityFrameworkCore;
using Oracul.Data.Data;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;

namespace Oracul.Data.Repositories
{
    /// <summary>
    /// User repository implementation with specific user-related methods
    /// </summary>
    public class UserRepository : Repository<User>, IUserRepository
    {
        public UserRepository(OraculDbContext context) : base(context)
        {
        }

        public async Task<User?> GetByEmailAsync(string email)
        {
            return await _dbSet.FirstOrDefaultAsync(u => u.Email == email);
        }

        public async Task<User?> GetByEmailWithRolesAsync(string email)
        {
            return await _dbSet
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Email == email);
        }

        public async Task<IEnumerable<User>> GetActiveUsersAsync()
        {
            return await _dbSet
                .Where(u => u.IsActive)
                .ToListAsync();
        }

        public async Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName)
        {
            return await _dbSet
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .Where(u => u.UserRoles.Any(ur => ur.Role.Name == roleName))
                .ToListAsync();
        }

        public async Task<bool> EmailExistsAsync(string email)
        {
            return await _dbSet.AnyAsync(u => u.Email == email);
        }

        public async Task<bool> EmailExistsAsync(string email, int excludeUserId)
        {
            return await _dbSet.AnyAsync(u => u.Email == email && u.Id != excludeUserId);
        }

        public async Task<User?> GetUserWithRolesAndPermissionsAsync(int userId)
        {
            return await _dbSet
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .ThenInclude(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .FirstOrDefaultAsync(u => u.Id == userId);
        }

        public async Task<IEnumerable<string>> GetUserPermissionsAsync(int userId)
        {
            var permissions = await _dbSet
                .Where(u => u.Id == userId)
                .SelectMany(u => u.UserRoles)
                .SelectMany(ur => ur.Role.RolePermissions)
                .Select(rp => rp.Permission.Name)
                .Distinct()
                .ToListAsync();

            return permissions;
        }

        public async Task UpdateLastLoginAsync(int userId)
        {
            var user = await GetByIdAsync(userId);
            if (user != null)
            {
                user.LastLoginAt = DateTime.UtcNow;
                Update(user);
            }
        }

        // Oracle-specific methods
        public async Task<IEnumerable<User>> GetOraclesAsync()
        {
            return await _dbSet
                .Where(u => u.IsOracle && u.IsActive)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToListAsync();
        }

        public async Task<User?> GetOracleWithServicesAsync(int oracleId)
        {
            return await _dbSet
                .Include(u => u.OracleServices)
                .ThenInclude(os => os.Service)
                .FirstOrDefaultAsync(u => u.Id == oracleId && u.IsOracle);
        }

        public async Task<IEnumerable<User>> GetAvailableOraclesAsync()
        {
            return await _dbSet
                .Where(u => u.IsOracle && u.IsActive && u.IsAvailable)
                .OrderBy(u => u.FirstName)
                .ThenBy(u => u.LastName)
                .ToListAsync();
        }
    }
}
