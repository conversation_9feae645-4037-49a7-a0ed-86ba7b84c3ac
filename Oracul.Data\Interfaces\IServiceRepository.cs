using Oracul.Data.Models;

namespace Oracul.Data.Interfaces
{
    /// <summary>
    /// Repository interface for Service entity operations
    /// </summary>
    public interface IServiceRepository : IRepository<Service>
    {
        /// <summary>
        /// Get services by category
        /// </summary>
        Task<IEnumerable<Service>> GetServicesByCategoryAsync(string category);

        /// <summary>
        /// Get active services only
        /// </summary>
        Task<IEnumerable<Service>> GetActiveServicesAsync();
    }
}
