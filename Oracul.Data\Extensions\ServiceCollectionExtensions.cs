using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Oracul.Data.Data;
using Oracul.Data.Interfaces;
using Oracul.Data.Repositories;

namespace Oracul.Data.Extensions
{
    /// <summary>
    /// Extension methods for registering data layer services
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Registers all data layer services including DbContext, repositories, and Unit of Work
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <param name="connectionStringName">The name of the connection string in configuration (default: "DefaultConnection")</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddOraculDataLayer(
            this IServiceCollection services, 
            IConfiguration configuration,
            string connectionStringName = "DefaultConnection")
        {
            // Register DbContext
            services.AddDbContext<OraculDbContext>(options =>
            {
                var connectionString = configuration.GetConnectionString(connectionStringName);
                options.UseSqlServer(connectionString);
                
                // Enable sensitive data logging in development
                #if DEBUG
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
                #endif
            });

            // Register repositories
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

            // Register Unit of Work
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            return services;
        }

        /// <summary>
        /// Registers data layer services with a custom DbContext configuration
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="optionsAction">The DbContext options configuration action</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddOraculDataLayer(
            this IServiceCollection services,
            Action<DbContextOptionsBuilder> optionsAction)
        {
            // Register DbContext with custom options
            services.AddDbContext<OraculDbContext>(optionsAction);

            // Register repositories
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));

            // Register Unit of Work
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            return services;
        }
    }
}
