<div class="demo-container">
  <!-- Header -->
  <mat-card class="header-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>view_module</mat-icon>
        Profile Card Component Demo
      </mat-card-title>
      <mat-card-subtitle>
        Showcasing the new profile card component with sample oracle profiles
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p>
        This demo showcases the new <code>ProfileCardComponent</code> that displays the most important 
        information from oracle profiles in a compact, visually appealing card format. The cards are 
        designed to work in both grid and list layouts.
      </p>
    </mat-card-content>
    <mat-card-actions>
      <button mat-stroked-button routerLink="/profile-demo">
        <mat-icon>arrow_back</mat-icon>
        Back to Profile Demo
      </button>
      <button mat-stroked-button routerLink="/profiles/search">
        <mat-icon>search</mat-icon>
        Live Search
      </button>
    </mat-card-actions>
  </mat-card>

  <!-- Two-Column Grid Demo -->
  <mat-card class="grid-demo-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>grid_view</mat-icon>
        Two-Column Grid Layout
      </mat-card-title>
      <mat-card-subtitle>
        Profile cards displayed in a responsive two-column grid (as requested)
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <div class="profiles-grid">
        <app-profile-card 
          *ngFor="let profile of sampleProfiles"
          [profile]="profile"
          [compact]="true"
          (contactClicked)="onContactProfile($event)"
          (profileClicked)="onProfileClicked($event)">
        </app-profile-card>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Full-Width Cards Demo -->
  <mat-card class="list-demo-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>view_list</mat-icon>
        Full-Width Card Layout
      </mat-card-title>
      <mat-card-subtitle>
        Profile cards displayed in full-width format with more details
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <div class="profiles-list">
        <app-profile-card 
          *ngFor="let profile of sampleProfiles"
          [profile]="profile"
          [compact]="false"
          (contactClicked)="onContactProfile($event)"
          (profileClicked)="onProfileClicked($event)">
        </app-profile-card>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Features Overview -->
  <mat-card class="features-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>star</mat-icon>
        Profile Card Features
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="features-grid">
        <div class="feature-item">
          <mat-icon>person</mat-icon>
          <div class="feature-content">
            <h4>Profile Information</h4>
            <p>Name, title, location, and years of experience</p>
          </div>
        </div>
        
        <div class="feature-item">
          <mat-icon>info</mat-icon>
          <div class="feature-content">
            <h4>About Section</h4>
            <p>Truncated summary with smart text limiting</p>
          </div>
        </div>
        
        <div class="feature-item">
          <mat-icon>psychology</mat-icon>
          <div class="feature-content">
            <h4>Skills & Expertise</h4>
            <p>Top skills displayed as chips with endorsement counts</p>
          </div>
        </div>
        
        <div class="feature-item">
          <mat-icon>contact_mail</mat-icon>
          <div class="feature-content">
            <h4>Contact Information</h4>
            <p>Email, phone, and website with direct action buttons</p>
          </div>
        </div>
        
        <div class="feature-item">
          <mat-icon>visibility</mat-icon>
          <div class="feature-content">
            <h4>Profile Stats</h4>
            <p>Views, endorsements, and project counts</p>
          </div>
        </div>
        
        <div class="feature-item">
          <mat-icon>touch_app</mat-icon>
          <div class="feature-content">
            <h4>Interactive Actions</h4>
            <p>View profile, contact, and share buttons</p>
          </div>
        </div>
        
        <div class="feature-item">
          <mat-icon>phone_android</mat-icon>
          <div class="feature-content">
            <h4>Responsive Design</h4>
            <p>Optimized for desktop, tablet, and mobile devices</p>
          </div>
        </div>
        
        <div class="feature-item">
          <mat-icon>palette</mat-icon>
          <div class="feature-content">
            <h4>Material Design</h4>
            <p>Consistent with app theme and Material Design principles</p>
          </div>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Technical Details -->
  <mat-card class="technical-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>code</mat-icon>
        Technical Implementation
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="tech-details">
        <div class="tech-section">
          <h4>Component Features</h4>
          <ul>
            <li><strong>Reusable:</strong> Can be used in any part of the application</li>
            <li><strong>Configurable:</strong> Compact mode, button visibility options</li>
            <li><strong>Event-driven:</strong> Emits events for contact and profile clicks</li>
            <li><strong>Type-safe:</strong> Full TypeScript support with UserProfile interface</li>
          </ul>
        </div>
        
        <div class="tech-section">
          <h4>Usage Example</h4>
          <pre><code>&lt;app-profile-card 
  [profile]="userProfile"
  [compact]="true"
  [showContactButton]="true"
  [showViewProfileButton]="true"
  (contactClicked)="onContact($event)"
  (profileClicked)="onProfileClick($event)"&gt;
&lt;/app-profile-card&gt;</code></pre>
        </div>
        
        <div class="tech-section">
          <h4>Grid Layout CSS</h4>
          <pre><code>.profiles-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

@media (max-width: 768px) {
  .profiles-grid {
    grid-template-columns: 1fr;
  }
}</code></pre>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
