import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { map, tap, catchError } from 'rxjs/operators';
import {
  UserProfile,
  ProfileUpdateRequest,
  SkillEndorsementRequest,
  ProfileViewRequest,
  ProfileAnalytics,
  ProfileSearchFilters,
  ProfileSearchResult,
  BlogPost,
  Achievement,
  Certification,
  WorkExperience,
  PortfolioItem,
  ProfileSkill
} from '../models/profile.models';

@Injectable({
  providedIn: 'root'
})
export class ProfileService {
  private readonly API_URL = '/api/profiles';
  
  private currentProfileSubject = new BehaviorSubject<UserProfile | null>(null);
  public currentProfile$ = this.currentProfileSubject.asObservable();

  constructor(private http: HttpClient) {}

  // Profile CRUD Operations
  getProfile(identifier: string): Observable<UserProfile> {
    return this.http.get<UserProfile>(`${this.API_URL}/${identifier}`)
      .pipe(
        tap(profile => {
          if (profile) {
            this.currentProfileSubject.next(profile);
          }
        }),
        catchError(this.handleError)
      );
  }

  getCurrentUserProfile(): Observable<UserProfile> {
    return this.http.get<UserProfile>(`${this.API_URL}/me`)
      .pipe(
        tap(profile => this.currentProfileSubject.next(profile)),
        catchError(this.handleError)
      );
  }

  updateProfile(updates: ProfileUpdateRequest): Observable<UserProfile> {
    return this.http.put<UserProfile>(`${this.API_URL}/me`, updates)
      .pipe(
        tap(profile => this.currentProfileSubject.next(profile)),
        catchError(this.handleError)
      );
  }

  createProfile(profileData: Partial<UserProfile>): Observable<UserProfile> {
    return this.http.post<UserProfile>(`${this.API_URL}`, profileData)
      .pipe(
        tap(profile => this.currentProfileSubject.next(profile)),
        catchError(this.handleError)
      );
  }

  deleteProfile(): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/me`)
      .pipe(
        tap(() => this.currentProfileSubject.next(null)),
        catchError(this.handleError)
      );
  }

  // Profile Search
  searchProfiles(filters: ProfileSearchFilters, page: number = 1, limit: number = 20): Observable<ProfileSearchResult> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (filters.location) params = params.set('location', filters.location);
    if (filters.skills?.length) params = params.set('skills', filters.skills.join(','));
    if (filters.experience) params = params.set('experience', filters.experience);
    if (filters.availability !== undefined) params = params.set('availability', filters.availability.toString());
    if (filters.sortBy) params = params.set('sortBy', filters.sortBy);

    return this.http.get<ProfileSearchResult>(`${this.API_URL}/search`, { params })
      .pipe(catchError(this.handleError));
  }

  // Skills Management
  addSkill(skill: Omit<ProfileSkill, 'id' | 'endorsements' | 'isEndorsedByCurrentUser'>): Observable<ProfileSkill> {
    return this.http.post<ProfileSkill>(`${this.API_URL}/me/skills`, skill)
      .pipe(catchError(this.handleError));
  }

  updateSkill(skillId: number, updates: Partial<ProfileSkill>): Observable<ProfileSkill> {
    return this.http.put<ProfileSkill>(`${this.API_URL}/me/skills/${skillId}`, updates)
      .pipe(catchError(this.handleError));
  }

  deleteSkill(skillId: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/me/skills/${skillId}`)
      .pipe(catchError(this.handleError));
  }

  endorseSkill(request: SkillEndorsementRequest): Observable<void> {
    return this.http.post<void>(`${this.API_URL}/skills/endorse`, request)
      .pipe(catchError(this.handleError));
  }

  // Experience Management
  addExperience(experience: Omit<WorkExperience, 'id'>): Observable<WorkExperience> {
    return this.http.post<WorkExperience>(`${this.API_URL}/me/experiences`, experience)
      .pipe(catchError(this.handleError));
  }

  updateExperience(experienceId: number, updates: Partial<WorkExperience>): Observable<WorkExperience> {
    return this.http.put<WorkExperience>(`${this.API_URL}/me/experiences/${experienceId}`, updates)
      .pipe(catchError(this.handleError));
  }

  deleteExperience(experienceId: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/me/experiences/${experienceId}`)
      .pipe(catchError(this.handleError));
  }

  // Portfolio Management
  addPortfolioItem(item: Omit<PortfolioItem, 'id'>): Observable<PortfolioItem> {
    return this.http.post<PortfolioItem>(`${this.API_URL}/me/portfolio`, item)
      .pipe(catchError(this.handleError));
  }

  updatePortfolioItem(itemId: number, updates: Partial<PortfolioItem>): Observable<PortfolioItem> {
    return this.http.put<PortfolioItem>(`${this.API_URL}/me/portfolio/${itemId}`, updates)
      .pipe(catchError(this.handleError));
  }

  deletePortfolioItem(itemId: number): Observable<void> {
    return this.http.delete<void>(`${this.API_URL}/me/portfolio/${itemId}`)
      .pipe(catchError(this.handleError));
  }

  // Achievements & Certifications
  addAchievement(achievement: Omit<Achievement, 'id'>): Observable<Achievement> {
    return this.http.post<Achievement>(`${this.API_URL}/me/achievements`, achievement)
      .pipe(catchError(this.handleError));
  }

  addCertification(certification: Omit<Certification, 'id'>): Observable<Certification> {
    return this.http.post<Certification>(`${this.API_URL}/me/certifications`, certification)
      .pipe(catchError(this.handleError));
  }

  // Blog Posts
  getBlogPosts(profileId: number): Observable<BlogPost[]> {
    return this.http.get<BlogPost[]>(`${this.API_URL}/${profileId}/blog-posts`)
      .pipe(catchError(this.handleError));
  }

  // Analytics
  getProfileAnalytics(): Observable<ProfileAnalytics> {
    return this.http.get<ProfileAnalytics>(`${this.API_URL}/me/analytics`)
      .pipe(catchError(this.handleError));
  }

  recordProfileView(request: ProfileViewRequest): Observable<void> {
    return this.http.post<void>(`${this.API_URL}/views`, request)
      .pipe(catchError(this.handleError));
  }

  // File Upload
  uploadProfilePhoto(file: File): Observable<{ url: string }> {
    const formData = new FormData();
    formData.append('photo', file);
    
    return this.http.post<{ url: string }>(`${this.API_URL}/me/photo`, formData)
      .pipe(catchError(this.handleError));
  }

  uploadCoverPhoto(file: File): Observable<{ url: string }> {
    const formData = new FormData();
    formData.append('cover', file);
    
    return this.http.post<{ url: string }>(`${this.API_URL}/me/cover`, formData)
      .pipe(catchError(this.handleError));
  }

  // Utility Methods
  generateProfileSlug(firstName: string, lastName: string): Observable<{ slug: string }> {
    return this.http.post<{ slug: string }>(`${this.API_URL}/generate-slug`, { firstName, lastName })
      .pipe(catchError(this.handleError));
  }

  checkSlugAvailability(slug: string): Observable<{ available: boolean }> {
    return this.http.get<{ available: boolean }>(`${this.API_URL}/check-slug/${slug}`)
      .pipe(catchError(this.handleError));
  }

  // Social Sharing
  getProfileShareData(profileId: number): Observable<{ title: string; description: string; imageUrl: string; url: string }> {
    return this.http.get<{ title: string; description: string; imageUrl: string; url: string }>(`${this.API_URL}/${profileId}/share-data`)
      .pipe(catchError(this.handleError));
  }

  private handleError(error: any): Observable<never> {
    console.error('ProfileService error:', error);
    throw error;
  }
}
