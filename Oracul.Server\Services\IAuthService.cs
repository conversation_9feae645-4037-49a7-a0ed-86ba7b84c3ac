using Oracul.Server.Models;

namespace Oracul.Server.Services
{
    public interface IAuthService
    {
        /// <summary>
        /// Authenticates a user with email and password
        /// </summary>
        /// <param name="request">Login request</param>
        /// <returns>Authentication response</returns>
        Task<AuthResponse> LoginAsync(LoginRequest request);

        /// <summary>
        /// Registers a new user
        /// </summary>
        /// <param name="request">Registration request</param>
        /// <returns>Authentication response</returns>
        Task<AuthResponse> RegisterAsync(RegisterRequest request);

        /// <summary>
        /// Initiates password reset process
        /// </summary>
        /// <param name="request">Forgot password request</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<object>> ForgotPasswordAsync(ForgotPasswordRequest request);

        /// <summary>
        /// Resets user password using token
        /// </summary>
        /// <param name="request">Reset password request</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<object>> ResetPasswordAsync(ResetPasswordRequest request);

        /// <summary>
        /// Changes user password
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="request">Change password request</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<object>> ChangePasswordAsync(int userId, ChangePasswordRequest request);

        /// <summary>
        /// Refreshes access token using refresh token
        /// </summary>
        /// <param name="request">Refresh token request</param>
        /// <returns>New authentication response</returns>
        Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request);

        /// <summary>
        /// Logs out user by invalidating refresh token
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<object>> LogoutAsync(int userId);

        /// <summary>
        /// Confirms user email
        /// </summary>
        /// <param name="email">User email</param>
        /// <param name="token">Confirmation token</param>
        /// <returns>Success response</returns>
        Task<ApiResponse<object>> ConfirmEmailAsync(string email, string token);

        /// <summary>
        /// Authenticates user via social provider
        /// </summary>
        /// <param name="request">Social login request</param>
        /// <returns>Authentication response</returns>
        Task<AuthResponse> SocialLoginAsync(SocialLoginRequest request);

        /// <summary>
        /// Gets user information by ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>User information</returns>
        Task<UserInfo?> GetUserInfoAsync(int userId);
    }
}
