using Oracul.Data.Models;

namespace Oracul.Data.Interfaces
{
    /// <summary>
    /// Specific repository interface for User entity with additional methods
    /// </summary>
    public interface IUserRepository : IRepository<User>
    {
        // User-specific methods
        Task<User?> GetByEmailAsync(string email);
        Task<User?> GetByEmailWithRolesAsync(string email);
        Task<IEnumerable<User>> GetActiveUsersAsync();
        Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName);
        Task<bool> EmailExistsAsync(string email);
        Task<bool> EmailExistsAsync(string email, int excludeUserId);
        Task<User?> GetUserWithRolesAndPermissionsAsync(int userId);
        Task<IEnumerable<string>> GetUserPermissionsAsync(int userId);
        Task UpdateLastLoginAsync(int userId);

        // Oracle-specific methods
        Task<IEnumerable<User>> GetOraclesAsync();
        Task<User?> GetOracleWithServicesAsync(int oracleId);
        Task<IEnumerable<User>> GetAvailableOraclesAsync();
    }
}
