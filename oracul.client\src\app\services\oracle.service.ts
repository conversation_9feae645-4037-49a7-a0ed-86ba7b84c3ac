import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from '../../environments/environment';

export interface OracleProfile {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  profilePictureUrl?: string;
  about?: string;
  hourlyRate?: number;
  specializations: string[];
  yearsOfExperience?: number;
  languages: string[];
  isAvailable: boolean;
  services: OracleService[];
  lastLoginAt?: Date;
}

export interface OracleService {
  serviceId: number;
  name: string;
  description?: string;
  price: number;
  durationMinutes: number;
  category: string;
  notes?: string;
}

export interface Service {
  id: number;
  name: string;
  description?: string;
  price: number;
  durationMinutes: number;
  category: string;
  isActive: boolean;
}

export interface UpdateOracleProfileRequest {
  about?: string;
  hourlyRate?: number;
  specializations: string[];
  yearsOfExperience?: number;
  languages: string[];
  isAvailable: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data?: T;
  errors: string[];
}

@Injectable({
  providedIn: 'root'
})
export class OracleService {
  private readonly API_URL = `${environment.apiUrl}/api/oracle`;

  constructor(private http: HttpClient) { }

  /**
   * Get all oracle profiles
   */
  getOracles(): Observable<ApiResponse<OracleProfile[]>> {
    return this.http.get<ApiResponse<OracleProfile[]>>(this.API_URL)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get oracle profile by ID
   */
  getOracleProfile(id: number): Observable<ApiResponse<OracleProfile>> {
    return this.http.get<ApiResponse<OracleProfile>>(`${this.API_URL}/${id}`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Get available oracles
   */
  getAvailableOracles(): Observable<ApiResponse<OracleProfile[]>> {
    return this.http.get<ApiResponse<OracleProfile[]>>(`${this.API_URL}/available`)
      .pipe(catchError(this.handleError));
  }

  /**
   * Update oracle profile
   */
  updateOracleProfile(id: number, request: UpdateOracleProfileRequest): Observable<ApiResponse<OracleProfile>> {
    return this.http.put<ApiResponse<OracleProfile>>(`${this.API_URL}/${id}`, request)
      .pipe(catchError(this.handleError));
  }

  private handleError(error: any): Observable<never> {
    console.error('Oracle service error:', error);
    return throwError(() => error);
  }
}
