.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  gap: 20px;
  display: flex;
  flex-direction: column;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.error-content mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
}

/* Profile Header */
.profile-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.profile-image-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  min-width: 200px;
}

.profile-image {
  width: 180px;
  height: 180px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid #e0e0e0;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.availability-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  background-color: #f5f5f5;
  color: #666;
}

.availability-badge.available {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.availability-badge mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.profile-info {
  flex: 1;
}

.profile-info h1 {
  margin: 0 0 16px 0;
  font-size: 2.5rem;
  font-weight: 400;
  color: #333;
}

.experience,
.hourly-rate {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
  font-size: 1.1rem;
  color: #666;
}

.experience mat-icon,
.hourly-rate mat-icon {
  color: #ff6f00;
}

.contact-actions {
  display: flex;
  gap: 12px;
  margin-top: 24px;
  flex-wrap: wrap;
}

/* Content Sections */
.about-section,
.specializations-section,
.languages-section,
.services-section,
.no-services-section {
  margin-bottom: 20px;
}

.about-text {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #555;
  margin: 0;
}

/* Chips */
.chips-container {
  margin-top: 8px;
}

mat-chip-set {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

mat-chip {
  background-color: #f0f0f0;
  color: #333;
}

/* Services */
.service-category {
  margin-bottom: 32px;
}

.category-title {
  font-size: 1.5rem;
  font-weight: 500;
  color: #333;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.service-card {
  border: 1px solid #e0e0e0;
  transition: box-shadow 0.3s ease;
}

.service-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.service-description {
  color: #666;
  margin: 8px 0;
  line-height: 1.5;
}

.service-notes {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  color: #555;
  font-style: italic;
  margin: 8px 0;
  padding: 8px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.service-notes mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  margin-top: 2px;
  color: #2196f3;
}

.service-price {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}

.price {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2e7d32;
}

/* No Services */
.no-services-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-services-content mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #bbb;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-image-section {
    min-width: auto;
  }

  .profile-image {
    width: 150px;
    height: 150px;
  }

  .profile-info h1 {
    font-size: 2rem;
  }

  .contact-actions {
    justify-content: center;
  }

  .services-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .profile-image {
    width: 120px;
    height: 120px;
  }

  .profile-info h1 {
    font-size: 1.8rem;
  }

  .contact-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
