using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;
using Oracul.Server.Models;
using System.Text.Json;

namespace Oracul.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class OracleController : ControllerBase
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OracleController> _logger;

        public OracleController(IUnitOfWork unitOfWork, ILogger<OracleController> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Get all oracle profiles
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<IEnumerable<OracleProfileDto>>>> GetOracles()
        {
            try
            {
                var oracles = await _unitOfWork.Users.GetOraclesAsync();
                var oracleProfiles = oracles.Select(MapToOracleProfileDto).ToList();

                return Ok(new ApiResponse<IEnumerable<OracleProfileDto>>
                {
                    Success = true,
                    Data = oracleProfiles,
                    Message = "Oracles retrieved successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving oracles");
                return StatusCode(500, new ApiResponse<IEnumerable<OracleProfileDto>>
                {
                    Success = false,
                    Message = "Internal server error",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Get oracle profile by ID
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<OracleProfileDto>>> GetOracleProfile(int id)
        {
            try
            {
                var oracle = await _unitOfWork.Users.GetOracleWithServicesAsync(id);
                
                if (oracle == null)
                {
                    return NotFound(new ApiResponse<OracleProfileDto>
                    {
                        Success = false,
                        Message = "Oracle not found"
                    });
                }

                var oracleProfile = MapToOracleProfileDto(oracle);

                return Ok(new ApiResponse<OracleProfileDto>
                {
                    Success = true,
                    Data = oracleProfile,
                    Message = "Oracle profile retrieved successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving oracle profile for ID: {OracleId}", id);
                return StatusCode(500, new ApiResponse<OracleProfileDto>
                {
                    Success = false,
                    Message = "Internal server error",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Get available oracles
        /// </summary>
        [HttpGet("available")]
        public async Task<ActionResult<ApiResponse<IEnumerable<OracleProfileDto>>>> GetAvailableOracles()
        {
            try
            {
                var oracles = await _unitOfWork.Users.GetAvailableOraclesAsync();
                var oracleProfiles = oracles.Select(MapToOracleProfileDto).ToList();

                return Ok(new ApiResponse<IEnumerable<OracleProfileDto>>
                {
                    Success = true,
                    Data = oracleProfiles,
                    Message = "Available oracles retrieved successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving available oracles");
                return StatusCode(500, new ApiResponse<IEnumerable<OracleProfileDto>>
                {
                    Success = false,
                    Message = "Internal server error",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Update oracle profile (requires authentication)
        /// </summary>
        [HttpPut("{id}")]
        [Authorize]
        public async Task<ActionResult<ApiResponse<OracleProfileDto>>> UpdateOracleProfile(int id, [FromBody] UpdateOracleProfileRequest request)
        {
            try
            {
                var oracle = await _unitOfWork.Users.GetByIdAsync(id);
                
                if (oracle == null)
                {
                    return NotFound(new ApiResponse<OracleProfileDto>
                    {
                        Success = false,
                        Message = "Oracle not found"
                    });
                }

                // Update oracle properties
                oracle.About = request.About;
                oracle.HourlyRate = request.HourlyRate;
                oracle.Specializations = JsonSerializer.Serialize(request.Specializations);
                oracle.YearsOfExperience = request.YearsOfExperience;
                oracle.Languages = JsonSerializer.Serialize(request.Languages);
                oracle.IsAvailable = request.IsAvailable;
                oracle.IsOracle = true; // Ensure user is marked as oracle

                _unitOfWork.Users.Update(oracle);
                await _unitOfWork.SaveChangesAsync();

                var updatedOracle = await _unitOfWork.Users.GetOracleWithServicesAsync(id);
                var oracleProfile = MapToOracleProfileDto(updatedOracle!);

                return Ok(new ApiResponse<OracleProfileDto>
                {
                    Success = true,
                    Data = oracleProfile,
                    Message = "Oracle profile updated successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating oracle profile for ID: {OracleId}", id);
                return StatusCode(500, new ApiResponse<OracleProfileDto>
                {
                    Success = false,
                    Message = "Internal server error",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        private static OracleProfileDto MapToOracleProfileDto(User oracle)
        {
            var specializations = new List<string>();
            var languages = new List<string>();

            if (!string.IsNullOrEmpty(oracle.Specializations))
            {
                try
                {
                    specializations = JsonSerializer.Deserialize<List<string>>(oracle.Specializations) ?? new List<string>();
                }
                catch
                {
                    // If deserialization fails, treat as empty list
                }
            }

            if (!string.IsNullOrEmpty(oracle.Languages))
            {
                try
                {
                    languages = JsonSerializer.Deserialize<List<string>>(oracle.Languages) ?? new List<string>();
                }
                catch
                {
                    // If deserialization fails, treat as empty list
                }
            }

            var services = oracle.OracleServices?.Where(os => os.IsActive && os.Service.IsActive)
                .Select(os => new OracleServiceDto
                {
                    ServiceId = os.ServiceId,
                    Name = os.Service.Name,
                    Description = os.Service.Description,
                    Price = os.CustomPrice ?? os.Service.Price,
                    DurationMinutes = os.CustomDurationMinutes ?? os.Service.DurationMinutes,
                    Category = os.Service.Category,
                    Notes = os.Notes
                }).ToList() ?? new List<OracleServiceDto>();

            return new OracleProfileDto
            {
                Id = oracle.Id,
                FirstName = oracle.FirstName,
                LastName = oracle.LastName,
                Email = oracle.Email,
                PhoneNumber = oracle.PhoneNumber,
                ProfilePictureUrl = oracle.ProfilePictureUrl,
                About = oracle.About,
                HourlyRate = oracle.HourlyRate,
                Specializations = specializations,
                YearsOfExperience = oracle.YearsOfExperience,
                Languages = languages,
                IsAvailable = oracle.IsAvailable,
                Services = services,
                LastLoginAt = oracle.LastLoginAt
            };
        }
    }
}
