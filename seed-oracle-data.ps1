# PowerShell script to update database and seed oracle data
param(
    [Parameter(Mandatory=$false)]
    [string]$ApiUrl = "https://localhost:7154"
)

Write-Host "Oracle Data Seeding Script" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host "API URL: $ApiUrl" -ForegroundColor Cyan
Write-Host ""

# Step 1: Update database with migrations
Write-Host "Step 1: Updating database with latest migrations..." -ForegroundColor Yellow
dotnet ef database update --project Oracul.Data --startup-project Oracul.Server

if ($LASTEXITCODE -ne 0) {
    Write-Host "Database update failed. Please check your connection string and ensure SQL Server is running." -ForegroundColor Red
    exit 1
}

Write-Host "Database updated successfully!" -ForegroundColor Green
Write-Host ""

# Step 2: Start the server (in background)
Write-Host "Step 2: Starting the server..." -ForegroundColor Yellow
$serverProcess = Start-Process -FilePath "dotnet" -ArgumentList "run --project Oracul.Server" -PassThru -WindowStyle Hidden

# Wait a bit for server to start
Write-Host "Waiting for server to start..." -ForegroundColor Cyan
Start-Sleep -Seconds 10

# Step 3: Call seeding endpoint
Write-Host "Step 3: Seeding oracle data..." -ForegroundColor Yellow

try {
    # Make HTTP request to seed data
    $response = Invoke-RestMethod -Uri "$ApiUrl/api/seed/oracle-data" -Method POST -ContentType "application/json"
    
    if ($response.success) {
        Write-Host "Oracle data seeded successfully!" -ForegroundColor Green
        Write-Host "Message: $($response.message)" -ForegroundColor Cyan
        Write-Host "Data: $($response.data)" -ForegroundColor Cyan
    } else {
        Write-Host "Seeding failed: $($response.message)" -ForegroundColor Red
        if ($response.errors) {
            Write-Host "Errors:" -ForegroundColor Red
            $response.errors | ForEach-Object { Write-Host "  - $_" -ForegroundColor Red }
        }
    }
} catch {
    Write-Host "Error calling seeding endpoint: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure the server is running and accessible at $ApiUrl" -ForegroundColor Yellow
}

# Step 4: Stop the server
Write-Host ""
Write-Host "Step 4: Stopping the server..." -ForegroundColor Yellow
if ($serverProcess -and !$serverProcess.HasExited) {
    Stop-Process -Id $serverProcess.Id -Force
    Write-Host "Server stopped." -ForegroundColor Green
} else {
    Write-Host "Server process not found or already stopped." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Seeding process completed!" -ForegroundColor Green
Write-Host ""
Write-Host "Sample Oracle Users Created:" -ForegroundColor Cyan
Write-Host "  - Luna Starweaver (<EMAIL>) - Tarot Reader" -ForegroundColor White
Write-Host "  - Marcus Celestial (<EMAIL>) - Astrologer" -ForegroundColor White
Write-Host "  - Sage Moonchild (<EMAIL>) - Crystal Healer" -ForegroundColor White
Write-Host "  - River Palmistry (<EMAIL>) - Palm Reader" -ForegroundColor White
Write-Host "  - Aurora Wisdom (<EMAIL>) - Spiritual Counselor" -ForegroundColor White
Write-Host ""
Write-Host "You can now start your application and view the oracle profiles!" -ForegroundColor Green
