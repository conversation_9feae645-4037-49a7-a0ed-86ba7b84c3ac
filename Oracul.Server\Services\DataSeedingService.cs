using Oracul.Data.Interfaces;
using Oracul.Data.Models;
using System.Text.Json;

namespace Oracul.Server.Services
{
    /// <summary>
    /// Service for seeding sample data into the database
    /// </summary>
    public class DataSeedingService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<DataSeedingService> _logger;

        public DataSeedingService(IUnitOfWork unitOfWork, ILogger<DataSeedingService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        /// <summary>
        /// Seed sample oracle users and their services
        /// </summary>
        public async Task SeedOracleDataAsync()
        {
            try
            {
                _logger.LogInformation("Starting oracle data seeding...");

                // Check if oracle role exists
                var oracleRole = (await _unitOfWork.Roles.GetAllAsync())
                    .FirstOrDefault(r => r.Name == "Oracle");

                if (oracleRole == null)
                {
                    _logger.LogWarning("Oracle role not found. Creating it...");
                    oracleRole = new Role
                    {
                        Name = "Oracle",
                        Description = "Oracle service provider",
                        IsActive = true
                    };
                    await _unitOfWork.Roles.AddAsync(oracleRole);
                    await _unitOfWork.SaveChangesAsync();
                }

                // Create sample oracle users
                await CreateSampleOraclesAsync(oracleRole.Id);

                // Assign services to oracles
                await AssignServicesToOraclesAsync();

                _logger.LogInformation("Oracle data seeding completed successfully!");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while seeding oracle data");
                throw;
            }
        }

        private async Task CreateSampleOraclesAsync(int oracleRoleId)
        {
            var sampleOracles = new[]
            {
                new
                {
                    FirstName = "Luna",
                    LastName = "Starweaver",
                    Email = "<EMAIL>",
                    About = "Experienced tarot reader and spiritual guide with over 15 years of practice. I specialize in love, career, and spiritual growth readings using traditional Rider-Waite tarot deck.",
                    HourlyRate = 85.00m,
                    YearsOfExperience = 15,
                    Specializations = new[] { "Tarot Reading", "Love & Relationships", "Career Guidance", "Spiritual Growth" },
                    Languages = new[] { "English", "Spanish", "French" },
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400"
                },
                new
                {
                    FirstName = "Marcus",
                    LastName = "Celestial",
                    Email = "<EMAIL>",
                    About = "Professional astrologer and birth chart specialist. I help clients understand their cosmic blueprint and navigate life's challenges through the wisdom of the stars.",
                    HourlyRate = 95.00m,
                    YearsOfExperience = 12,
                    Specializations = new[] { "Astrology", "Birth Charts", "Compatibility", "Life Transitions" },
                    Languages = new[] { "English", "Italian" },
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400"
                },
                new
                {
                    FirstName = "Sage",
                    LastName = "Moonchild",
                    Email = "<EMAIL>",
                    About = "Crystal healer and energy worker dedicated to helping others find balance and healing. I combine crystal therapy with meditation and chakra balancing.",
                    HourlyRate = 70.00m,
                    YearsOfExperience = 8,
                    Specializations = new[] { "Crystal Healing", "Energy Work", "Chakra Balancing", "Meditation" },
                    Languages = new[] { "English", "Portuguese" },
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400"
                },
                new
                {
                    FirstName = "River",
                    LastName = "Palmistry",
                    Email = "<EMAIL>",
                    About = "Third-generation palm reader with a gift for seeing life's path through the lines of your hands. I provide insights into personality, relationships, and future possibilities.",
                    HourlyRate = 60.00m,
                    YearsOfExperience = 20,
                    Specializations = new[] { "Palm Reading", "Life Path", "Personality Analysis", "Future Insights" },
                    Languages = new[] { "English", "Hindi", "Urdu" },
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400"
                },
                new
                {
                    FirstName = "Aurora",
                    LastName = "Wisdom",
                    Email = "<EMAIL>",
                    About = "Spiritual counselor and life coach helping people find their purpose and overcome challenges. I offer compassionate guidance for all of life's journeys.",
                    HourlyRate = 90.00m,
                    YearsOfExperience = 18,
                    Specializations = new[] { "Spiritual Counseling", "Life Coaching", "Purpose Discovery", "Emotional Healing" },
                    Languages = new[] { "English", "German", "Dutch" },
                    ProfilePictureUrl = "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400"
                }
            };

            foreach (var oracleData in sampleOracles)
            {
                // Check if user already exists
                var existingUser = await _unitOfWork.Users.GetByEmailAsync(oracleData.Email);
                if (existingUser != null)
                {
                    _logger.LogInformation($"User {oracleData.Email} already exists, skipping...");
                    continue;
                }

                // Create new oracle user
                var oracle = new User
                {
                    FirstName = oracleData.FirstName,
                    LastName = oracleData.LastName,
                    Email = oracleData.Email,
                    IsActive = true,
                    EmailConfirmed = true,
                    IsOracle = true,
                    IsAvailable = true,
                    About = oracleData.About,
                    HourlyRate = oracleData.HourlyRate,
                    YearsOfExperience = oracleData.YearsOfExperience,
                    Specializations = JsonSerializer.Serialize(oracleData.Specializations),
                    Languages = JsonSerializer.Serialize(oracleData.Languages),
                    ProfilePictureUrl = oracleData.ProfilePictureUrl
                };

                await _unitOfWork.Users.AddAsync(oracle);
                await _unitOfWork.SaveChangesAsync();

                // Assign oracle role
                var userRole = new UserRole
                {
                    UserId = oracle.Id,
                    RoleId = oracleRoleId
                };
                await _unitOfWork.UserRoles.AddAsync(userRole);

                _logger.LogInformation($"Created oracle user: {oracle.FirstName} {oracle.LastName}");
            }

            await _unitOfWork.SaveChangesAsync();
        }

        private async Task AssignServicesToOraclesAsync()
        {
            var oracles = await _unitOfWork.Users.GetOraclesAsync();
            var services = await _unitOfWork.Services.GetActiveServicesAsync();

            var serviceAssignments = new Dictionary<string, int[]>
            {
                ["<EMAIL>"] = new[] { 1, 5 }, // Tarot Reading, Spiritual Guidance
                ["<EMAIL>"] = new[] { 2 }, // Astrology Consultation
                ["<EMAIL>"] = new[] { 4, 5 }, // Crystal Healing, Spiritual Guidance
                ["<EMAIL>"] = new[] { 3 }, // Palm Reading
                ["<EMAIL>"] = new[] { 5 } // Spiritual Guidance
            };

            foreach (var oracle in oracles)
            {
                if (serviceAssignments.TryGetValue(oracle.Email, out var serviceIds))
                {
                    foreach (var serviceId in serviceIds)
                    {
                        // Check if assignment already exists
                        var existingAssignment = (await _unitOfWork.OracleServices.GetAllAsync())
                            .FirstOrDefault(os => os.UserId == oracle.Id && os.ServiceId == serviceId);

                        if (existingAssignment == null)
                        {
                            var oracleService = new OracleService
                            {
                                UserId = oracle.Id,
                                ServiceId = serviceId,
                                IsActive = true,
                                Notes = $"Specialized service offered by {oracle.FirstName}"
                            };

                            await _unitOfWork.OracleServices.AddAsync(oracleService);
                        }
                    }
                }
            }

            await _unitOfWork.SaveChangesAsync();
            _logger.LogInformation("Service assignments completed");
        }
    }
}
