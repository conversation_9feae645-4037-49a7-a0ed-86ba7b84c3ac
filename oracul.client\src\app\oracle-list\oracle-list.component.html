<!-- Oracle List Page -->
<div class="oracle-list-container">
  <!-- Header -->
  <mat-card class="header-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>psychology</mat-icon>
        Oracle Directory
      </mat-card-title>
      <mat-card-subtitle>
        Find and connect with spiritual advisors and service providers
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <!-- Search and Filters -->
      <div class="search-filters">
        <mat-form-field appearance="outline" class="search-field">
          <mat-label>Search oracles...</mat-label>
          <input matInput 
                 [(ngModel)]="searchTerm" 
                 (input)="onSearchChange()"
                 placeholder="Name, specialization, or service">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>
        
        <div class="filter-controls">
          <mat-checkbox 
            [(ngModel)]="showAvailableOnly" 
            (change)="onAvailabilityFilterChange()">
            Available only
          </mat-checkbox>
          
          <button mat-icon-button (click)="refresh()" matTooltip="Refresh">
            <mat-icon>refresh</mat-icon>
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-card class="loading-card">
      <mat-card-content>
        <div class="loading-content">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading oracles...</p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-card class="error-card">
      <mat-card-content>
        <div class="error-content">
          <mat-icon color="warn">error</mat-icon>
          <h3>Error</h3>
          <p>{{ error }}</p>
          <button mat-raised-button color="primary" (click)="refresh()">
            <mat-icon>refresh</mat-icon>
            Try Again
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Oracle Grid -->
  <div *ngIf="!loading && !error" class="oracle-grid">
    <!-- No Results -->
    <div *ngIf="filteredOracles.length === 0" class="no-results">
      <mat-card>
        <mat-card-content>
          <div class="no-results-content">
            <mat-icon>search_off</mat-icon>
            <h3>No oracles found</h3>
            <p *ngIf="searchTerm || showAvailableOnly">
              Try adjusting your search criteria or filters.
            </p>
            <p *ngIf="!searchTerm && !showAvailableOnly">
              No oracles are currently registered in the system.
            </p>
            <button mat-raised-button color="primary" (click)="searchTerm = ''; showAvailableOnly = false; applyFilters()" 
                    *ngIf="searchTerm || showAvailableOnly">
              Clear Filters
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Oracle Cards -->
    <mat-card *ngFor="let oracle of filteredOracles" class="oracle-card" (click)="viewProfile(oracle)">
      <mat-card-header>
        <div mat-card-avatar class="oracle-avatar">
          <img [src]="oracle.profilePictureUrl || '/assets/default-avatar.png'" 
               [alt]="getFullName(oracle)"
               (error)="$event.target.src='/assets/default-avatar.png'">
        </div>
        <mat-card-title>{{ getFullName(oracle) }}</mat-card-title>
        <mat-card-subtitle>
          <div class="oracle-subtitle">
            <span *ngIf="oracle.yearsOfExperience" class="experience">
              <mat-icon>star</mat-icon>
              {{ getExperienceText(oracle) }}
            </span>
            <span class="availability" [class.available]="oracle.isAvailable">
              <mat-icon>{{ oracle.isAvailable ? 'check_circle' : 'schedule' }}</mat-icon>
              {{ oracle.isAvailable ? 'Available' : 'Busy' }}
            </span>
          </div>
        </mat-card-subtitle>
      </mat-card-header>
      
      <mat-card-content>
        <!-- About (truncated) -->
        <p *ngIf="oracle.about" class="about-preview">
          {{ oracle.about.length > 120 ? (oracle.about | slice:0:120) + '...' : oracle.about }}
        </p>
        
        <!-- Hourly Rate -->
        <div *ngIf="oracle.hourlyRate" class="hourly-rate">
          <mat-icon>attach_money</mat-icon>
          <span>{{ formatPrice(oracle.hourlyRate) }}/hour</span>
        </div>
        
        <!-- Specializations -->
        <div *ngIf="oracle.specializations.length > 0" class="specializations">
          <mat-chip-set>
            <mat-chip *ngFor="let spec of oracle.specializations.slice(0, 3)">
              {{ spec }}
            </mat-chip>
            <mat-chip *ngIf="oracle.specializations.length > 3" class="more-chip">
              +{{ oracle.specializations.length - 3 }} more
            </mat-chip>
          </mat-chip-set>
        </div>
        
        <!-- Service Categories -->
        <div *ngIf="oracle.services.length > 0" class="service-categories">
          <div class="service-info">
            <mat-icon>room_service</mat-icon>
            <span>{{ oracle.services.length }} service{{ oracle.services.length !== 1 ? 's' : '' }}</span>
            <span class="categories">
              ({{ getServiceCategories(oracle).join(', ') }})
            </span>
          </div>
        </div>
      </mat-card-content>
      
      <mat-card-actions>
        <button mat-button color="primary">
          <mat-icon>visibility</mat-icon>
          View Profile
        </button>
        <button mat-button *ngIf="oracle.email">
          <mat-icon>email</mat-icon>
          Contact
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Results Summary -->
  <div *ngIf="!loading && !error && filteredOracles.length > 0" class="results-summary">
    <mat-card>
      <mat-card-content>
        <p>
          Showing {{ filteredOracles.length }} of {{ oracles.length }} oracle{{ oracles.length !== 1 ? 's' : '' }}
          <span *ngIf="searchTerm || showAvailableOnly"> (filtered)</span>
        </p>
      </mat-card-content>
    </mat-card>
  </div>
</div>
