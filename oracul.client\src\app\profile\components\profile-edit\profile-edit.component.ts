import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormArray } from '@angular/forms';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';

import { ProfileService } from '../../services/profile.service';
import { AuthService } from '../../../auth/services/auth.service';
import { UserProfile, ProfileUpdateRequest } from '../../models/profile.models';

@Component({
  selector: 'app-profile-edit',
  templateUrl: './profile-edit.component.html',
  styleUrls: ['./profile-edit.component.css']
})
export class ProfileEditComponent implements OnInit, OnDestroy {
  profileForm: FormGroup;
  profile: UserProfile | null = null;
  isLoading = true;
  isSaving = false;
  
  private destroy$ = new Subject<void>();

  constructor(
    private formBuilder: FormBuilder,
    private profileService: ProfileService,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.profileForm = this.createForm();
  }

  ngOnInit(): void {
    this.loadProfile();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private createForm(): FormGroup {
    return this.formBuilder.group({
      // Basic Information
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      professionalTitle: [''],
      headline: ['', Validators.maxLength(220)],
      summary: ['', Validators.maxLength(2000)],
      
      // Location
      location: this.formBuilder.group({
        city: [''],
        state: [''],
        country: [''],
        displayLocation: ['']
      }),
      
      // Contact Information
      contactInfo: this.formBuilder.group({
        email: ['', Validators.email],
        isEmailPublic: [false],
        website: [''],
        portfolioUrl: [''],
        phoneNumbers: this.formBuilder.array([]),
        businessAddress: this.formBuilder.group({
          street: [''],
          city: [''],
          state: [''],
          postalCode: [''],
          country: [''],
          isPublic: [false]
        })
      }),
      
      // Privacy Settings
      isPublic: [true],
      
      // Social Links
      socialLinks: this.formBuilder.array([])
    });
  }

  private loadProfile(): void {
    this.profileService.getCurrentUserProfile()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (profile) => {
          this.profile = profile;
          this.populateForm(profile);
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading profile:', error);
          this.snackBar.open('Error loading profile', 'Close', { duration: 5000 });
          this.router.navigate(['/dashboard']);
          this.isLoading = false;
        }
      });
  }

  private populateForm(profile: UserProfile): void {
    this.profileForm.patchValue({
      firstName: profile.firstName,
      lastName: profile.lastName,
      professionalTitle: profile.professionalTitle || '',
      headline: profile.headline || '',
      summary: profile.summary || '',
      location: {
        city: profile.location?.city || '',
        state: profile.location?.state || '',
        country: profile.location?.country || '',
        displayLocation: profile.location?.displayLocation || ''
      },
      contactInfo: {
        email: profile.contactInfo.email || '',
        isEmailPublic: profile.contactInfo.isEmailPublic,
        website: profile.contactInfo.website || '',
        portfolioUrl: profile.contactInfo.portfolioUrl || '',
        businessAddress: {
          street: profile.contactInfo.businessAddress?.street || '',
          city: profile.contactInfo.businessAddress?.city || '',
          state: profile.contactInfo.businessAddress?.state || '',
          postalCode: profile.contactInfo.businessAddress?.postalCode || '',
          country: profile.contactInfo.businessAddress?.country || '',
          isPublic: profile.contactInfo.businessAddress?.isPublic || false
        }
      },
      isPublic: profile.isPublic
    });

    // Populate phone numbers
    this.setPhoneNumbers(profile.contactInfo.phoneNumbers || []);
    
    // Populate social links
    this.setSocialLinks(profile.socialLinks || []);
  }

  // Phone Numbers Management
  get phoneNumbers(): FormArray {
    return this.profileForm.get('contactInfo.phoneNumbers') as FormArray;
  }

  private setPhoneNumbers(phones: any[]): void {
    const phoneArray = this.phoneNumbers;
    phoneArray.clear();
    
    phones.forEach(phone => {
      phoneArray.push(this.formBuilder.group({
        id: [phone.id],
        number: [phone.number, Validators.required],
        type: [phone.type, Validators.required],
        isPublic: [phone.isPublic],
        isPrimary: [phone.isPrimary]
      }));
    });
  }

  addPhoneNumber(): void {
    const phoneGroup = this.formBuilder.group({
      id: [null],
      number: ['', Validators.required],
      type: ['mobile', Validators.required],
      isPublic: [false],
      isPrimary: [false]
    });
    
    this.phoneNumbers.push(phoneGroup);
  }

  removePhoneNumber(index: number): void {
    this.phoneNumbers.removeAt(index);
  }

  // Social Links Management
  get socialLinks(): FormArray {
    return this.profileForm.get('socialLinks') as FormArray;
  }

  private setSocialLinks(links: any[]): void {
    const linksArray = this.socialLinks;
    linksArray.clear();
    
    links.forEach(link => {
      linksArray.push(this.formBuilder.group({
        id: [link.id],
        platform: [link.platform, Validators.required],
        url: [link.url, [Validators.required, Validators.pattern('https?://.+')]],
        displayName: [link.displayName],
        isPublic: [link.isPublic]
      }));
    });
  }

  addSocialLink(): void {
    const linkGroup = this.formBuilder.group({
      id: [null],
      platform: ['linkedin', Validators.required],
      url: ['', [Validators.required, Validators.pattern('https?://.+')]],
      displayName: [''],
      isPublic: [true]
    });
    
    this.socialLinks.push(linkGroup);
  }

  removeSocialLink(index: number): void {
    this.socialLinks.removeAt(index);
  }

  // Form Submission
  onSubmit(): void {
    if (this.profileForm.valid) {
      this.isSaving = true;
      
      const formValue = this.profileForm.value;
      const updateRequest: ProfileUpdateRequest = {
        professionalTitle: formValue.professionalTitle,
        headline: formValue.headline,
        location: formValue.location,
        contactInfo: formValue.contactInfo,
        summary: formValue.summary,
        isPublic: formValue.isPublic
      };

      this.profileService.updateProfile(updateRequest)
        .pipe(takeUntil(this.destroy$))
        .subscribe({
          next: (updatedProfile) => {
            this.isSaving = false;
            this.snackBar.open('Profile updated successfully!', 'Close', { duration: 3000 });
            this.router.navigate(['/profile', updatedProfile.slug]);
          },
          error: (error) => {
            this.isSaving = false;
            console.error('Error updating profile:', error);
            this.snackBar.open('Error updating profile. Please try again.', 'Close', { duration: 5000 });
          }
        });
    } else {
      this.markFormGroupTouched();
      this.snackBar.open('Please fix the errors in the form', 'Close', { duration: 3000 });
    }
  }

  onCancel(): void {
    if (this.profile) {
      this.router.navigate(['/profile', this.profile.slug]);
    } else {
      this.router.navigate(['/dashboard']);
    }
  }

  // File Upload Methods
  onProfilePhotoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadProfilePhoto(file);
    }
  }

  onCoverPhotoSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      this.uploadCoverPhoto(file);
    }
  }

  private uploadProfilePhoto(file: File): void {
    this.profileService.uploadProfilePhoto(file)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (this.profile) {
            this.profile.profilePhotoUrl = response.url;
          }
          this.snackBar.open('Profile photo updated!', 'Close', { duration: 2000 });
        },
        error: (error) => {
          console.error('Error uploading profile photo:', error);
          this.snackBar.open('Error uploading photo', 'Close', { duration: 3000 });
        }
      });
  }

  private uploadCoverPhoto(file: File): void {
    this.profileService.uploadCoverPhoto(file)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          if (this.profile) {
            this.profile.coverPhotoUrl = response.url;
          }
          this.snackBar.open('Cover photo updated!', 'Close', { duration: 2000 });
        },
        error: (error) => {
          console.error('Error uploading cover photo:', error);
          this.snackBar.open('Error uploading photo', 'Close', { duration: 3000 });
        }
      });
  }

  // Utility Methods
  private markFormGroupTouched(): void {
    Object.keys(this.profileForm.controls).forEach(key => {
      const control = this.profileForm.get(key);
      control?.markAsTouched();
      
      if (control instanceof FormArray) {
        control.controls.forEach(arrayControl => {
          if (arrayControl instanceof FormGroup) {
            Object.keys(arrayControl.controls).forEach(arrayKey => {
              arrayControl.get(arrayKey)?.markAsTouched();
            });
          }
        });
      }
    });
  }

  getErrorMessage(fieldName: string): string {
    const control = this.profileForm.get(fieldName);
    if (control?.hasError('required')) {
      return `${fieldName} is required`;
    }
    if (control?.hasError('email')) {
      return 'Please enter a valid email address';
    }
    if (control?.hasError('minlength')) {
      return `${fieldName} must be at least ${control.errors?.['minlength'].requiredLength} characters`;
    }
    if (control?.hasError('maxlength')) {
      return `${fieldName} must be no more than ${control.errors?.['maxlength'].requiredLength} characters`;
    }
    if (control?.hasError('pattern')) {
      return 'Please enter a valid URL';
    }
    return '';
  }

  // Platform options for social links
  getPlatformOptions() {
    return [
      { value: 'linkedin', label: 'LinkedIn' },
      { value: 'twitter', label: 'Twitter' },
      { value: 'github', label: 'GitHub' },
      { value: 'behance', label: 'Behance' },
      { value: 'dribbble', label: 'Dribbble' },
      { value: 'instagram', label: 'Instagram' },
      { value: 'facebook', label: 'Facebook' },
      { value: 'youtube', label: 'YouTube' },
      { value: 'other', label: 'Other' }
    ];
  }

  // Phone type options
  getPhoneTypeOptions() {
    return [
      { value: 'mobile', label: 'Mobile' },
      { value: 'business', label: 'Business' },
      { value: 'home', label: 'Home' }
    ];
  }
}
