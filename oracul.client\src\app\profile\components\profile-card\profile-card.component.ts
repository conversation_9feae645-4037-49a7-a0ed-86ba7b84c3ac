import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { UserProfile } from '../../models/profile.models';

@Component({
  selector: 'app-profile-card',
  templateUrl: './profile-card.component.html',
  styleUrls: ['./profile-card.component.css']
})
export class ProfileCardComponent {
  @Input() profile!: UserProfile;
  @Input() showContactButton = true;
  @Input() showViewProfileButton = true;
  @Input() compact = false;
  @Output() contactClicked = new EventEmitter<UserProfile>();
  @Output() profileClicked = new EventEmitter<UserProfile>();

  constructor(private router: Router) {}

  get fullName(): string {
    return `${this.profile.firstName} ${this.profile.lastName}`;
  }

  get displayLocation(): string {
    return this.profile.location?.displayLocation || '';
  }

  get primaryEmail(): string {
    return this.profile.contactInfo?.email || '';
  }

  get primaryPhone(): string {
    const primaryPhone = this.profile.contactInfo?.phoneNumbers?.find(p => p.isPrimary && p.isPublic);
    return primaryPhone?.number || '';
  }

  get topSkills(): string[] {
    return this.profile.skills
      .sort((a, b) => b.endorsements - a.endorsements)
      .slice(0, this.compact ? 3 : 5)
      .map(skill => skill.name);
  }

  get experienceYears(): number {
    if (!this.profile.experiences?.length) return 0;
    
    const totalMonths = this.profile.experiences.reduce((total, exp) => {
      const startDate = new Date(exp.startDate);
      const endDate = exp.endDate ? new Date(exp.endDate) : new Date();
      const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 + 
                    (endDate.getMonth() - startDate.getMonth());
      return total + months;
    }, 0);
    
    return Math.floor(totalMonths / 12);
  }

  get hasContactInfo(): boolean {
    return !!(this.primaryEmail || this.primaryPhone || this.profile.contactInfo?.website);
  }

  get profileCompletionColor(): string {
    const percentage = this.profile.profileCompletionPercentage;
    if (percentage >= 80) return 'primary';
    if (percentage >= 60) return 'accent';
    return 'warn';
  }

  onViewProfile(): void {
    this.profileClicked.emit(this.profile);
    this.router.navigate(['/profile', this.profile.slug]);
  }

  onContact(): void {
    this.contactClicked.emit(this.profile);
  }

  onEmailClick(event: Event): void {
    event.stopPropagation();
    if (this.primaryEmail) {
      window.location.href = `mailto:${this.primaryEmail}`;
    }
  }

  onPhoneClick(event: Event): void {
    event.stopPropagation();
    if (this.primaryPhone) {
      window.location.href = `tel:${this.primaryPhone}`;
    }
  }

  onWebsiteClick(event: Event): void {
    event.stopPropagation();
    if (this.profile.contactInfo?.website) {
      window.open(this.profile.contactInfo.website, '_blank');
    }
  }

  formatPhoneNumber(phone: string): string {
    // Simple phone formatting - can be enhanced based on requirements
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    return phone;
  }

  truncateText(text: string, maxLength: number): string {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
  }

  getTotalEndorsements(): number {
    return this.profile.skills.reduce((total, skill) => total + skill.endorsements, 0);
  }
}
