import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { OracleService, OracleProfile } from '../services/oracle.service';

@Component({
  selector: 'app-oracle-list',
  templateUrl: './oracle-list.component.html',
  styleUrls: ['./oracle-list.component.css']
})
export class OracleListComponent implements OnInit {
  oracles: OracleProfile[] = [];
  filteredOracles: OracleProfile[] = [];
  loading = true;
  error: string | null = null;
  searchTerm = '';
  showAvailableOnly = false;

  constructor(
    private router: Router,
    private oracleService: OracleService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.loadOracles();
  }

  private loadOracles(): void {
    this.loading = true;
    this.error = null;

    this.oracleService.getOracles().subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.oracles = response.data;
          this.applyFilters();
        } else {
          this.error = response.message || 'Failed to load oracles';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading oracles:', error);
        this.error = 'Failed to load oracles';
        this.loading = false;
        this.snackBar.open('Failed to load oracles', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  onSearchChange(): void {
    this.applyFilters();
  }

  onAvailabilityFilterChange(): void {
    this.applyFilters();
  }

  applyFilters(): void {
    let filtered = [...this.oracles];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(oracle =>
        oracle.firstName.toLowerCase().includes(searchLower) ||
        oracle.lastName.toLowerCase().includes(searchLower) ||
        oracle.specializations.some(spec => spec.toLowerCase().includes(searchLower)) ||
        oracle.services.some(service => 
          service.name.toLowerCase().includes(searchLower) ||
          service.category.toLowerCase().includes(searchLower)
        )
      );
    }

    // Apply availability filter
    if (this.showAvailableOnly) {
      filtered = filtered.filter(oracle => oracle.isAvailable);
    }

    this.filteredOracles = filtered;
  }

  viewProfile(oracle: OracleProfile): void {
    this.router.navigate(['/oracle', oracle.id]);
  }

  getFullName(oracle: OracleProfile): string {
    return `${oracle.firstName} ${oracle.lastName}`;
  }

  getExperienceText(oracle: OracleProfile): string {
    if (!oracle.yearsOfExperience) return 'Experience not specified';
    const years = oracle.yearsOfExperience;
    return years === 1 ? '1 year' : `${years} years`;
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  getServiceCategories(oracle: OracleProfile): string[] {
    const categories = new Set(oracle.services.map(service => service.category));
    return Array.from(categories);
  }

  refresh(): void {
    this.loadOracles();
  }
}
