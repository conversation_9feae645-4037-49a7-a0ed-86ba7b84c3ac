using Microsoft.EntityFrameworkCore;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;
using Oracul.Server.Models;

namespace Oracul.Server.Services
{
    public class AuthService : IAuthService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IPasswordHashingService _passwordHashingService;
        private readonly IJwtService _jwtService;
        private readonly IEmailService _emailService;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            IUnitOfWork unitOfWork,
            IPasswordHashingService passwordHashingService,
            IJwtService jwtService,
            IEmailService emailService,
            ILogger<AuthService> logger)
        {
            _unitOfWork = unitOfWork;
            _passwordHashingService = passwordHashingService;
            _jwtService = jwtService;
            _emailService = emailService;
            _logger = logger;
        }

        public async Task<AuthResponse> LoginAsync(LoginRequest request)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByEmailWithRolesAsync(request.Email);
                
                if (user == null)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Invalid email or password"
                    };
                }

                // Check if account is locked
                if (user.LockoutEnd.HasValue && user.LockoutEnd > DateTime.UtcNow)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Account is temporarily locked due to multiple failed login attempts"
                    };
                }

                // Check if account is active
                if (!user.IsActive)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Account is deactivated"
                    };
                }

                // Verify password
                if (string.IsNullOrEmpty(user.PasswordHash) || !_passwordHashingService.VerifyPassword(request.Password, user.PasswordHash))
                {
                    // Increment failed login attempts
                    user.FailedLoginAttempts++;
                    
                    // Lock account after 5 failed attempts
                    if (user.FailedLoginAttempts >= 5)
                    {
                        user.LockoutEnd = DateTime.UtcNow.AddMinutes(30);
                        _logger.LogWarning("Account locked for user {Email} due to multiple failed login attempts", user.Email);
                    }

                    _unitOfWork.Users.Update(user);
                    await _unitOfWork.SaveChangesAsync();

                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Invalid email or password"
                    };
                }

                // Reset failed login attempts on successful login
                user.FailedLoginAttempts = 0;
                user.LockoutEnd = null;
                user.LastLoginAt = DateTime.UtcNow;

                // Generate refresh token
                var refreshToken = _jwtService.GenerateRefreshToken();
                user.RefreshToken = refreshToken;
                user.RefreshTokenExpiry = DateTime.UtcNow.AddDays(7);

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Get user roles and permissions
                var roles = user.UserRoles.Select(ur => ur.Role.Name).ToList();
                var permissions = await _unitOfWork.Users.GetUserPermissionsAsync(user.Id);

                // Generate access token
                var accessToken = _jwtService.GenerateAccessToken(user, roles, permissions);

                return new AuthResponse
                {
                    Success = true,
                    Message = "Login successful",
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(15), // Access token expiry
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        PhoneNumber = user.PhoneNumber,
                        ProfilePictureUrl = user.ProfilePictureUrl,
                        EmailConfirmed = user.EmailConfirmed,
                        LastLoginAt = user.LastLoginAt,
                        Roles = roles,
                        Permissions = permissions.ToList()
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for email {Email}", request.Email);
                return new AuthResponse
                {
                    Success = false,
                    Message = "An error occurred during login"
                };
            }
        }

        public async Task<AuthResponse> RegisterAsync(RegisterRequest request)
        {
            try
            {
                // Check if user already exists
                if (await _unitOfWork.Users.EmailExistsAsync(request.Email))
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "An account with this email already exists"
                    };
                }

                // Hash password
                var passwordHash = _passwordHashingService.HashPassword(request.Password);

                // Generate email confirmation token
                var emailConfirmationToken = _passwordHashingService.GenerateSecureToken();

                // Create user
                var user = new User
                {
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Email = request.Email,
                    PhoneNumber = request.PhoneNumber,
                    PasswordHash = passwordHash,
                    EmailConfirmationToken = emailConfirmationToken,
                    IsActive = true,
                    EmailConfirmed = false
                };

                await _unitOfWork.Users.AddAsync(user);
                await _unitOfWork.SaveChangesAsync();

                // Assign default "User" role
                var userRole = await _unitOfWork.Roles.FirstOrDefaultAsync(r => r.Name == "User");
                if (userRole != null)
                {
                    var userRoleAssignment = new UserRole
                    {
                        UserId = user.Id,
                        RoleId = userRole.Id
                    };
                    await _unitOfWork.UserRoles.AddAsync(userRoleAssignment);
                    await _unitOfWork.SaveChangesAsync();
                }

                // Send confirmation email
                await _emailService.SendEmailConfirmationAsync(user.Email, emailConfirmationToken, $"{user.FirstName} {user.LastName}");

                _logger.LogInformation("User registered successfully: {Email}", user.Email);

                return new AuthResponse
                {
                    Success = true,
                    Message = "Registration successful. Please check your email to confirm your account."
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for email {Email}", request.Email);
                return new AuthResponse
                {
                    Success = false,
                    Message = "An error occurred during registration"
                };
            }
        }

        public async Task<ApiResponse<object>> ForgotPasswordAsync(ForgotPasswordRequest request)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByEmailAsync(request.Email);

                if (user == null)
                {
                    // Don't reveal if email exists or not for security
                    return new ApiResponse<object>
                    {
                        Success = true,
                        Message = "If an account with this email exists, a password reset link has been sent."
                    };
                }

                // Generate password reset token
                var resetToken = _passwordHashingService.GenerateSecureToken();
                user.PasswordResetToken = resetToken;
                user.PasswordResetTokenExpiry = DateTime.UtcNow.AddHours(1);

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Send password reset email
                await _emailService.SendPasswordResetEmailAsync(user.Email, resetToken, $"{user.FirstName} {user.LastName}");

                _logger.LogInformation("Password reset requested for email {Email}", user.Email);

                return new ApiResponse<object>
                {
                    Success = true,
                    Message = "If an account with this email exists, a password reset link has been sent."
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during forgot password for email {Email}", request.Email);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "An error occurred while processing your request"
                };
            }
        }

        public async Task<ApiResponse<object>> ResetPasswordAsync(ResetPasswordRequest request)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByEmailAsync(request.Email);

                if (user == null ||
                    string.IsNullOrEmpty(user.PasswordResetToken) ||
                    user.PasswordResetToken != request.Token ||
                    user.PasswordResetTokenExpiry < DateTime.UtcNow)
                {
                    return new ApiResponse<object>
                    {
                        Success = false,
                        Message = "Invalid or expired reset token"
                    };
                }

                // Hash new password
                user.PasswordHash = _passwordHashingService.HashPassword(request.NewPassword);
                user.PasswordResetToken = null;
                user.PasswordResetTokenExpiry = null;
                user.FailedLoginAttempts = 0;
                user.LockoutEnd = null;

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Password reset successful for email {Email}", user.Email);

                return new ApiResponse<object>
                {
                    Success = true,
                    Message = "Password has been reset successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password reset for email {Email}", request.Email);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "An error occurred while resetting your password"
                };
            }
        }

        public async Task<ApiResponse<object>> ChangePasswordAsync(int userId, ChangePasswordRequest request)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);

                if (user == null)
                {
                    return new ApiResponse<object>
                    {
                        Success = false,
                        Message = "User not found"
                    };
                }

                // Verify current password
                if (string.IsNullOrEmpty(user.PasswordHash) || !_passwordHashingService.VerifyPassword(request.CurrentPassword, user.PasswordHash))
                {
                    return new ApiResponse<object>
                    {
                        Success = false,
                        Message = "Current password is incorrect"
                    };
                }

                // Hash new password
                user.PasswordHash = _passwordHashingService.HashPassword(request.NewPassword);

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("Password changed successfully for user {UserId}", userId);

                return new ApiResponse<object>
                {
                    Success = true,
                    Message = "Password changed successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during password change for user {UserId}", userId);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "An error occurred while changing your password"
                };
            }
        }

        public async Task<AuthResponse> RefreshTokenAsync(RefreshTokenRequest request)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByEmailWithRolesAsync(
                    (await _unitOfWork.Users.FindAsync(u => u.RefreshToken == request.RefreshToken)).FirstOrDefault()?.Email ?? "");

                if (user == null ||
                    user.RefreshToken != request.RefreshToken ||
                    user.RefreshTokenExpiry < DateTime.UtcNow)
                {
                    return new AuthResponse
                    {
                        Success = false,
                        Message = "Invalid or expired refresh token"
                    };
                }

                // Generate new tokens
                var newRefreshToken = _jwtService.GenerateRefreshToken();
                user.RefreshToken = newRefreshToken;
                user.RefreshTokenExpiry = DateTime.UtcNow.AddDays(7);

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Get user roles and permissions
                var roles = user.UserRoles.Select(ur => ur.Role.Name).ToList();
                var permissions = await _unitOfWork.Users.GetUserPermissionsAsync(user.Id);

                // Generate new access token
                var accessToken = _jwtService.GenerateAccessToken(user, roles, permissions);

                return new AuthResponse
                {
                    Success = true,
                    Message = "Token refreshed successfully",
                    AccessToken = accessToken,
                    RefreshToken = newRefreshToken,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(15)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return new AuthResponse
                {
                    Success = false,
                    Message = "An error occurred while refreshing token"
                };
            }
        }

        public async Task<ApiResponse<object>> LogoutAsync(int userId)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByIdAsync(userId);

                if (user != null)
                {
                    user.RefreshToken = null;
                    user.RefreshTokenExpiry = null;

                    _unitOfWork.Users.Update(user);
                    await _unitOfWork.SaveChangesAsync();
                }

                return new ApiResponse<object>
                {
                    Success = true,
                    Message = "Logged out successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout for user {UserId}", userId);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "An error occurred during logout"
                };
            }
        }

        public async Task<ApiResponse<object>> ConfirmEmailAsync(string email, string token)
        {
            try
            {
                var user = await _unitOfWork.Users.GetByEmailAsync(email);

                if (user == null || user.EmailConfirmationToken != token)
                {
                    return new ApiResponse<object>
                    {
                        Success = false,
                        Message = "Invalid confirmation token"
                    };
                }

                user.EmailConfirmed = true;
                user.EmailConfirmationToken = null;

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Send welcome email
                await _emailService.SendWelcomeEmailAsync(user.Email, $"{user.FirstName} {user.LastName}");

                _logger.LogInformation("Email confirmed for user {Email}", user.Email);

                return new ApiResponse<object>
                {
                    Success = true,
                    Message = "Email confirmed successfully"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during email confirmation for {Email}", email);
                return new ApiResponse<object>
                {
                    Success = false,
                    Message = "An error occurred while confirming email"
                };
            }
        }

        public async Task<AuthResponse> SocialLoginAsync(SocialLoginRequest request)
        {
            try
            {
                User? user = null;

                // Find user by social provider ID
                if (request.Provider.ToLower() == "google" && !string.IsNullOrEmpty(request.ProviderId))
                {
                    user = (await _unitOfWork.Users.FindAsync(u => u.GoogleId == request.ProviderId)).FirstOrDefault();
                }
                else if (request.Provider.ToLower() == "facebook" && !string.IsNullOrEmpty(request.ProviderId))
                {
                    user = (await _unitOfWork.Users.FindAsync(u => u.FacebookId == request.ProviderId)).FirstOrDefault();
                }

                // If user not found by provider ID, try to find by email
                if (user == null && !string.IsNullOrEmpty(request.Email))
                {
                    user = await _unitOfWork.Users.GetByEmailWithRolesAsync(request.Email);

                    // Link social account to existing user
                    if (user != null)
                    {
                        if (request.Provider.ToLower() == "google")
                            user.GoogleId = request.ProviderId;
                        else if (request.Provider.ToLower() == "facebook")
                            user.FacebookId = request.ProviderId;

                        if (!string.IsNullOrEmpty(request.ProfilePictureUrl))
                            user.ProfilePictureUrl = request.ProfilePictureUrl;
                    }
                }

                // Create new user if not found
                if (user == null)
                {
                    user = new User
                    {
                        FirstName = request.FirstName ?? "",
                        LastName = request.LastName ?? "",
                        Email = request.Email ?? "",
                        EmailConfirmed = true, // Social accounts are pre-verified
                        IsActive = true,
                        ProfilePictureUrl = request.ProfilePictureUrl
                    };

                    if (request.Provider.ToLower() == "google")
                        user.GoogleId = request.ProviderId;
                    else if (request.Provider.ToLower() == "facebook")
                        user.FacebookId = request.ProviderId;

                    await _unitOfWork.Users.AddAsync(user);
                    await _unitOfWork.SaveChangesAsync();

                    // Assign default "User" role
                    var userRole = await _unitOfWork.Roles.FirstOrDefaultAsync(r => r.Name == "User");
                    if (userRole != null)
                    {
                        var userRoleAssignment = new UserRole
                        {
                            UserId = user.Id,
                            RoleId = userRole.Id
                        };
                        await _unitOfWork.UserRoles.AddAsync(userRoleAssignment);
                        await _unitOfWork.SaveChangesAsync();
                    }
                }

                // Update last login and generate tokens
                user.LastLoginAt = DateTime.UtcNow;
                var refreshToken = _jwtService.GenerateRefreshToken();
                user.RefreshToken = refreshToken;
                user.RefreshTokenExpiry = DateTime.UtcNow.AddDays(7);

                _unitOfWork.Users.Update(user);
                await _unitOfWork.SaveChangesAsync();

                // Get user with roles for token generation
                user = await _unitOfWork.Users.GetByEmailWithRolesAsync(user.Email);
                var roles = user?.UserRoles.Select(ur => ur.Role.Name).ToList() ?? new List<string>();
                var permissions = await _unitOfWork.Users.GetUserPermissionsAsync(user?.Id ?? 0);

                // Generate access token
                var accessToken = _jwtService.GenerateAccessToken(user!, roles, permissions);

                return new AuthResponse
                {
                    Success = true,
                    Message = "Social login successful",
                    AccessToken = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.UtcNow.AddMinutes(15),
                    User = new UserInfo
                    {
                        Id = user!.Id,
                        Email = user.Email,
                        FirstName = user.FirstName,
                        LastName = user.LastName,
                        PhoneNumber = user.PhoneNumber,
                        ProfilePictureUrl = user.ProfilePictureUrl,
                        EmailConfirmed = user.EmailConfirmed,
                        LastLoginAt = user.LastLoginAt,
                        Roles = roles,
                        Permissions = permissions.ToList()
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during social login with provider {Provider}", request.Provider);
                return new AuthResponse
                {
                    Success = false,
                    Message = "An error occurred during social login"
                };
            }
        }

        public async Task<UserInfo?> GetUserInfoAsync(int userId)
        {
            try
            {
                var user = await _unitOfWork.Users.GetUserWithRolesAndPermissionsAsync(userId);

                if (user == null)
                    return null;

                var roles = user.UserRoles.Select(ur => ur.Role.Name).ToList();
                var permissions = user.UserRoles
                    .SelectMany(ur => ur.Role.RolePermissions)
                    .Select(rp => rp.Permission.Name)
                    .Distinct()
                    .ToList();

                return new UserInfo
                {
                    Id = user.Id,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    PhoneNumber = user.PhoneNumber,
                    ProfilePictureUrl = user.ProfilePictureUrl,
                    EmailConfirmed = user.EmailConfirmed,
                    LastLoginAt = user.LastLoginAt,
                    Roles = roles,
                    Permissions = permissions
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user info for user {UserId}", userId);
                return null;
            }
        }
    }
}
