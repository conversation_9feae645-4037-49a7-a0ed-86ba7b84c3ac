using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Junction entity for Oracle-Service many-to-many relationship
    /// </summary>
    public class OracleService : BaseEntity
    {
        [Required]
        public int UserId { get; set; } // Oracle (User) ID

        [Required]
        public int ServiceId { get; set; }

        // Custom pricing for this oracle's service (overrides default service price)
        public decimal? CustomPrice { get; set; }

        // Custom duration for this oracle's service (overrides default duration)
        public int? CustomDurationMinutes { get; set; }

        public bool IsActive { get; set; } = true;

        [MaxLength(500)]
        public string? Notes { get; set; }

        // Navigation properties
        public virtual User User { get; set; } = null!;
        public virtual Service Service { get; set; } = null!;
    }
}
