# Simple PowerShell script to seed oracle data
Write-Host "Oracle Data Seeding - Simple Version" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

# Step 1: Update database
Write-Host "Step 1: Updating database..." -ForegroundColor Yellow
dotnet ef database update --project Oracul.Data --startup-project Oracul.Server

if ($LASTEXITCODE -ne 0) {
    Write-Host "Database update failed!" -ForegroundColor Red
    exit 1
}

Write-Host "Database updated successfully!" -ForegroundColor Green
Write-Host ""

# Step 2: Instructions for manual seeding
Write-Host "Step 2: Manual Seeding Instructions" -ForegroundColor Yellow
Write-Host "====================================" -ForegroundColor Yellow
Write-Host ""
Write-Host "To seed the oracle data, follow these steps:" -ForegroundColor Cyan
Write-Host ""
Write-Host "1. Start the server:" -ForegroundColor White
Write-Host "   dotnet run --project Oracul.Server" -ForegroundColor Gray
Write-Host ""
Write-Host "2. Open a new terminal and make a POST request to seed data:" -ForegroundColor White
Write-Host "   curl -X POST https://localhost:7154/api/seed/oracle-data" -ForegroundColor Gray
Write-Host ""
Write-Host "   OR use PowerShell:" -ForegroundColor White
Write-Host "   Invoke-RestMethod -Uri 'https://localhost:7154/api/seed/oracle-data' -Method POST" -ForegroundColor Gray
Write-Host ""
Write-Host "3. Alternatively, you can use Swagger UI:" -ForegroundColor White
Write-Host "   - Navigate to https://localhost:7154/swagger" -ForegroundColor Gray
Write-Host "   - Find the 'Seed' controller" -ForegroundColor Gray
Write-Host "   - Execute the POST /api/seed/oracle-data endpoint" -ForegroundColor Gray
Write-Host ""
Write-Host "Sample Oracle Users that will be created:" -ForegroundColor Cyan
Write-Host "  - Luna Starweaver - Tarot Reader & Spiritual Guide" -ForegroundColor White
Write-Host "  - Marcus Celestial - Professional Astrologer" -ForegroundColor White
Write-Host "  - Sage Moonchild - Crystal Healer & Energy Worker" -ForegroundColor White
Write-Host "  - River Palmistry - Third-generation Palm Reader" -ForegroundColor White
Write-Host "  - Aurora Wisdom - Spiritual Counselor & Life Coach" -ForegroundColor White
Write-Host ""
Write-Host "Each oracle will have:" -ForegroundColor Cyan
Write-Host "  ✓ Complete profile with photo and description" -ForegroundColor Green
Write-Host "  ✓ Specializations and languages" -ForegroundColor Green
Write-Host "  ✓ Years of experience and hourly rates" -ForegroundColor Green
Write-Host "  ✓ Assigned services with custom pricing" -ForegroundColor Green
Write-Host ""
Write-Host "After seeding, you can view the oracles at:" -ForegroundColor Cyan
Write-Host "  Frontend: http://localhost:4200/oracles" -ForegroundColor White
Write-Host "  API: https://localhost:7154/api/oracle" -ForegroundColor White
