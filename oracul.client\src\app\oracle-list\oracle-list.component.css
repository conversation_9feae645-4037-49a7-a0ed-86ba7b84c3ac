.oracle-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Header */
.header-card {
  margin-bottom: 24px;
}

.search-filters {
  display: flex;
  gap: 16px;
  align-items: flex-end;
  flex-wrap: wrap;
}

.search-field {
  flex: 1;
  min-width: 300px;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Loading and Error States */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}

.loading-content,
.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
  padding: 20px;
}

.error-content mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
}

/* Oracle Grid */
.oracle-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.oracle-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e0e0e0;
}

.oracle-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* Oracle Avatar */
.oracle-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
}

.oracle-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Oracle Subtitle */
.oracle-subtitle {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.experience {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  color: #666;
}

.experience mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  color: #ff6f00;
}

.availability {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.85rem;
  color: #999;
}

.availability.available {
  color: #2e7d32;
}

.availability mat-icon {
  font-size: 14px;
  width: 14px;
  height: 14px;
}

/* Card Content */
.about-preview {
  color: #666;
  line-height: 1.4;
  margin: 12px 0;
  font-size: 0.95rem;
}

.hourly-rate {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #2e7d32;
  font-weight: 500;
  margin: 8px 0;
}

.hourly-rate mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.specializations {
  margin: 12px 0;
}

.specializations mat-chip-set {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.specializations mat-chip {
  font-size: 0.8rem;
  height: 28px;
  background-color: #f0f0f0;
}

.more-chip {
  background-color: #e3f2fd !important;
  color: #1976d2 !important;
}

.service-categories {
  margin: 12px 0;
}

.service-info {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 0.9rem;
}

.service-info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

.categories {
  font-style: italic;
  color: #888;
}

/* No Results */
.no-results {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
}

.no-results-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-results-content mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  color: #bbb;
}

/* Results Summary */
.results-summary {
  margin-top: 24px;
}

.results-summary mat-card-content {
  padding: 12px 16px;
}

.results-summary p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .oracle-list-container {
    padding: 16px;
  }

  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-field {
    min-width: auto;
  }

  .filter-controls {
    justify-content: space-between;
  }

  .oracle-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .oracle-list-container {
    padding: 12px;
  }

  .oracle-card {
    margin: 0;
  }

  .oracle-subtitle {
    font-size: 0.85rem;
  }

  .about-preview {
    font-size: 0.9rem;
  }
}
