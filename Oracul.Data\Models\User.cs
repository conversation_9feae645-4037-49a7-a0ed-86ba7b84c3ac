using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// User entity representing application users
    /// </summary>
    public class User : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [Required]
        [MaxLength(255)]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [MaxLength(20)]
        public string? PhoneNumber { get; set; }

        public string? PasswordHash { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime? LastLoginAt { get; set; }

        public bool EmailConfirmed { get; set; } = false;

        public string? EmailConfirmationToken { get; set; }

        public string? PasswordResetToken { get; set; }

        public DateTime? PasswordResetTokenExpiry { get; set; }

        // Social Authentication
        public string? GoogleId { get; set; }

        public string? FacebookId { get; set; }

        public string? ProfilePictureUrl { get; set; }

        // Security
        public string? RefreshToken { get; set; }

        public DateTime? RefreshTokenExpiry { get; set; }

        public int FailedLoginAttempts { get; set; } = 0;

        public DateTime? LockoutEnd { get; set; }

        // Oracle-specific properties
        public string? About { get; set; }
        public bool IsOracle { get; set; } = false;
        public decimal? HourlyRate { get; set; }
        public string? Specializations { get; set; } // JSON array of specializations
        public int? YearsOfExperience { get; set; }
        public string? Languages { get; set; } // JSON array of languages
        public bool IsAvailable { get; set; } = true;

        // Navigation properties
        public virtual ICollection<UserRole> UserRoles { get; set; } = new List<UserRole>();
        public virtual ICollection<OracleService> OracleServices { get; set; } = new List<OracleService>();
    }
}
