﻿:host {
  display: block;
  width: 100%;
  height: 100%;
}

.app-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 500;
  margin-left: 8px;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.nav-links {
  display: flex;
  margin-left: 32px;
  gap: 8px;
}

.nav-links button {
  color: rgba(255, 255, 255, 0.9);
}

.nav-links button.active {
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-links button mat-icon {
  margin-right: 4px;
}

.toolbar-spacer {
  flex: 1 1 auto;
}

.auth-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-info {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.auth-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
}

.auth-buttons button {
  color: rgba(255, 255, 255, 0.9);
}

.auth-buttons mat-raised-button {
  color: #333;
}

.content-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header-card {
  margin-bottom: 20px;
}

.header-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-container {
  display: flex;
  justify-content: center;
  margin: 40px 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px;
}

.data-card {
  margin-top: 20px;
}

.data-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-links {
    display: none;
  }

  .app-title {
    font-size: 1.2rem;
  }

  .auth-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .auth-buttons button {
    font-size: 0.9rem;
    padding: 4px 8px;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 1rem;
  }

  .user-info {
    display: none;
  }
}

.table-container {
  overflow-x: auto;
  margin-top: 16px;
}

.weather-table {
  width: 100%;
}

.weather-table th {
  font-weight: 600;
}

.weather-table th mat-icon {
  margin-right: 8px;
  vertical-align: middle;
}

.temperature {
  font-weight: 500;
  color: #ff6b35;
}

.summary {
  font-style: italic;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .content-container {
    padding: 10px;
  }

  .weather-table {
    font-size: 14px;
  }
}

/* Material Design enhancements */
mat-card {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: box-shadow 0.3s ease;
}

mat-card:hover {
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

button[mat-raised-button] {
  margin: 8px 0;
}

button[mat-raised-button] mat-icon {
  margin-right: 8px;
}
