using Microsoft.AspNetCore.Mvc;
using Oracul.Server.Models;
using Oracul.Server.Services;

namespace Oracul.Server.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SeedController : ControllerBase
    {
        private readonly DataSeedingService _seedingService;
        private readonly ILogger<SeedController> _logger;

        public SeedController(DataSeedingService seedingService, ILogger<SeedController> logger)
        {
            _seedingService = seedingService;
            _logger = logger;
        }

        /// <summary>
        /// Seed oracle data (Development only)
        /// </summary>
        [HttpPost("oracle-data")]
        public async Task<ActionResult<ApiResponse<string>>> SeedOracleData()
        {
            try
            {
                _logger.LogInformation("Oracle data seeding requested");

                await _seedingService.SeedOracleDataAsync();

                return Ok(new ApiResponse<string>
                {
                    Success = true,
                    Message = "Oracle data seeded successfully",
                    Data = "Sample oracle users and services have been created"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding oracle data");
                return StatusCode(500, new ApiResponse<string>
                {
                    Success = false,
                    Message = "Failed to seed oracle data",
                    Errors = new List<string> { ex.Message }
                });
            }
        }

        /// <summary>
        /// Get seeding status
        /// </summary>
        [HttpGet("status")]
        public ActionResult<ApiResponse<object>> GetSeedingStatus()
        {
            return Ok(new ApiResponse<object>
            {
                Success = true,
                Message = "Seeding endpoints available",
                Data = new
                {
                    AvailableEndpoints = new[]
                    {
                        "POST /api/seed/oracle-data - Seed sample oracle users and services"
                    },
                    Note = "These endpoints should only be used in development environment"
                }
            });
        }
    }
}
