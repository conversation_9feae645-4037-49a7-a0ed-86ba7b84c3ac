﻿<!-- Authentication-aware Layout -->
<div class="app-container">
  <!-- Top Toolbar - Only show when authenticated -->
  <mat-toolbar color="primary" *ngIf="authService.isAuthenticated$ | async">
    <mat-icon>cloud</mat-icon>
    <span class="toolbar-spacer"></span>
    <span>Oracul App</span>
    <span class="toolbar-spacer"></span>

    <!-- User Info -->
    <span class="user-info" *ngIf="authService.currentUser$ | async as user">
      Welcome, {{ user.firstName }}!
    </span>

    <button mat-icon-button [matMenuTriggerFor]="menu">
      <mat-icon>account_circle</mat-icon>
    </button>
    <mat-menu #menu="matMenu">
      <button mat-menu-item>
        <mat-icon>person</mat-icon>
        <span>Profile</span>
      </button>
      <button mat-menu-item>
        <mat-icon>settings</mat-icon>
        <span>Settings</span>
      </button>
      <mat-divider></mat-divider>
      <button mat-menu-item (click)="logout()">
        <mat-icon>logout</mat-icon>
        <span>Logout</span>
      </button>
    </mat-menu>
  </mat-toolbar>

  <!-- Main Content Area -->
  <div class="content-container">
    <!-- Router Outlet for All Components -->
    <router-outlet></router-outlet>
  </div>
</div>
