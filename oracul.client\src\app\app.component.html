<!-- Authentication-aware Layout -->
<div class="app-container">
  <!-- Top Toolbar - Always show -->
  <mat-toolbar color="primary">
    <mat-icon>psychology</mat-icon>
    <span class="app-title" routerLink="/oracles">Oracul</span>

    <!-- Navigation Links -->
    <div class="nav-links">
      <button mat-button routerLink="/oracles" routerLinkActive="active">
        <mat-icon>psychology</mat-icon>
        Oracles
      </button>
      <button mat-button routerLink="/dashboard" routerLinkActive="active" *ngIf="authService.isAuthenticated$ | async">
        <mat-icon>dashboard</mat-icon>
        Dashboard
      </button>
    </div>

    <span class="toolbar-spacer"></span>

    <!-- Authentication Section -->
    <div *ngIf="authService.isAuthenticated$ | async; else authButtons" class="auth-section">
      <!-- User Info -->
      <span class="user-info" *ngIf="authService.currentUser$ | async as user">
        Welcome, {{ user.firstName }}!
      </span>

      <button mat-icon-button [matMenuTriggerFor]="menu">
        <mat-icon>account_circle</mat-icon>
      </button>
      <mat-menu #menu="matMenu">
        <button mat-menu-item>
          <mat-icon>person</mat-icon>
          <span>Profile</span>
        </button>
        <button mat-menu-item>
          <mat-icon>settings</mat-icon>
          <span>Settings</span>
        </button>
        <mat-divider></mat-divider>
        <button mat-menu-item (click)="logout()">
          <mat-icon>logout</mat-icon>
          <span>Logout</span>
        </button>
      </mat-menu>
    </div>

    <!-- Login/Register Buttons -->
    <ng-template #authButtons>
      <div class="auth-buttons">
        <button mat-button routerLink="/login">
          <mat-icon>login</mat-icon>
          Login
        </button>
        <button mat-raised-button color="accent" routerLink="/register">
          <mat-icon>person_add</mat-icon>
          Register
        </button>
      </div>
    </ng-template>
  </mat-toolbar>

  <!-- Main Content Area -->
  <div class="content-container">
    <!-- Router Outlet for All Components -->
    <router-outlet></router-outlet>
  </div>
</div>
