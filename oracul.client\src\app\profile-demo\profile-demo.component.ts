import { Component } from '@angular/core';

@Component({
  selector: 'app-profile-demo',
  template: `
    <div class="demo-container">
      <mat-card class="demo-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>person</mat-icon>
            Profile System Demo
          </mat-card-title>
          <mat-card-subtitle>
            Test the professional profile system with mockup data
          </mat-card-subtitle>
        </mat-card-header>

        <mat-card-content>
          <div class="demo-section">
            <h3>🎯 Available Profile Pages</h3>
            <div class="demo-links">
              <a mat-raised-button color="primary" routerLink="/profile/luna-starweaver">
                <mat-icon>visibility</mat-icon>
                View Astrologer Profile
              </a>

              <a mat-raised-button color="accent" routerLink="/profile/edit">
                <mat-icon>edit</mat-icon>
                Edit Profile
              </a>

              <a mat-stroked-button routerLink="/profiles/search">
                <mat-icon>search</mat-icon>
                Search Professionals
              </a>
            </div>
          </div>

          <mat-divider></mat-divider>

          <div class="demo-section">
            <h3>📋 Sample Profile Features</h3>
            <div class="features-grid">
              <div class="feature-item">
                <mat-icon>photo_camera</mat-icon>
                <span>Mystical Profile Photos</span>
              </div>
              <div class="feature-item">
                <mat-icon>work</mat-icon>
                <span>Spiritual Journey Timeline</span>
              </div>
              <div class="feature-item">
                <mat-icon>psychology</mat-icon>
                <span>Astrological Skills & Endorsements</span>
              </div>
              <div class="feature-item">
                <mat-icon>work_outline</mat-icon>
                <span>Reading Portfolio & Testimonials</span>
              </div>
              <div class="feature-item">
                <mat-icon>article</mat-icon>
                <span>Cosmic Wisdom Blog Posts</span>
              </div>
              <div class="feature-item">
                <mat-icon>emoji_events</mat-icon>
                <span>Certifications & Achievements</span>
              </div>
              <div class="feature-item">
                <mat-icon>contact_mail</mat-icon>
                <span>Sacred Contact Information</span>
              </div>
              <div class="feature-item">
                <mat-icon>analytics</mat-icon>
                <span>Spiritual Impact Analytics</span>
              </div>
            </div>
          </div>

          <mat-divider></mat-divider>

          <div class="demo-section">
            <h3>🔗 Direct URLs for Testing</h3>
            <div class="url-list">
              <div class="url-item">
                <code>http://localhost:4201/profile/luna-starweaver</code>
                <span class="url-description">Professional astrologer profile with cosmic data</span>
              </div>
              <div class="url-item">
                <code>http://localhost:4201/profile/edit</code>
                <span class="url-description">Profile editing form</span>
              </div>
              <div class="url-item">
                <code>http://localhost:4201/profiles/search</code>
                <span class="url-description">Professional search page</span>
              </div>
            </div>
          </div>

          <mat-divider></mat-divider>

          <div class="demo-section">
            <h3>💡 Mockup Data Info</h3>
            <div class="info-box">
              <mat-icon>info</mat-icon>
              <div class="info-content">
                <p><strong>Sample Profile:</strong> Luna Starweaver - Professional Astrologer & Cosmic Guide</p>
                <p><strong>Features:</strong> Complete mystical profile with celestial photos, spiritual journey, astrological skills, reading portfolio, cosmic blog posts, and spiritual analytics</p>
                <p><strong>Data Source:</strong> MockProfileService with realistic astrology-focused sample data</p>
                <p><strong>Images:</strong> High-quality celestial and mystical stock photos from Unsplash</p>
              </div>
            </div>
          </div>
        </mat-card-content>

        <mat-card-actions>
          <button mat-raised-button color="primary" routerLink="/profile/luna-starweaver">
            <mat-icon>launch</mat-icon>
            Start Astrology Profile Demo
          </button>
          <button mat-stroked-button routerLink="/dashboard">
            <mat-icon>dashboard</mat-icon>
            Back to Dashboard
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  `,
  styles: [`
    .demo-container {
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }

    .demo-card {
      margin-bottom: 20px;
    }

    .demo-section {
      margin: 24px 0;
    }

    .demo-section h3 {
      color: var(--theme-primary);
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .demo-links {
      display: flex;
      gap: 12px;
      flex-wrap: wrap;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .feature-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px;
      background-color: rgba(0,0,0,0.02);
      border-radius: 8px;
      border-left: 3px solid var(--theme-primary);
    }

    .feature-item mat-icon {
      color: var(--theme-primary);
    }

    .url-list {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .url-item {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 12px;
      background-color: rgba(0,0,0,0.02);
      border-radius: 8px;
    }

    .url-item code {
      background-color: var(--theme-primary);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
    }

    .url-description {
      color: var(--theme-text-secondary);
      font-size: 0.9rem;
    }

    .info-box {
      display: flex;
      gap: 12px;
      padding: 16px;
      background-color: rgba(103, 58, 183, 0.1);
      border-radius: 8px;
      border-left: 4px solid var(--theme-primary);
    }

    .info-box mat-icon {
      color: var(--theme-primary);
      margin-top: 2px;
    }

    .info-content p {
      margin: 4px 0;
      line-height: 1.4;
    }

    .info-content strong {
      color: var(--theme-primary);
    }

    mat-divider {
      margin: 20px 0;
    }

    @media (max-width: 600px) {
      .demo-container {
        padding: 10px;
      }

      .demo-links {
        flex-direction: column;
      }

      .features-grid {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ProfileDemoComponent {}
