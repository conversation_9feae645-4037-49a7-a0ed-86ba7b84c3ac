using Microsoft.EntityFrameworkCore;
using Oracul.Data.Models;

namespace Oracul.Data.Data
{
    /// <summary>
    /// Main database context for the Oracul application
    /// </summary>
    public class OraculDbContext : DbContext
    {
        public OraculDbContext(DbContextOptions<OraculDbContext> options) : base(options)
        {
        }

        // DbSets
        public DbSet<User> Users { get; set; }
        public DbSet<Role> Roles { get; set; }
        public DbSet<Permission> Permissions { get; set; }
        public DbSet<UserRole> UserRoles { get; set; }
        public DbSet<RolePermission> RolePermissions { get; set; }
        public DbSet<Service> Services { get; set; }
        public DbSet<OracleService> OracleServices { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure entity relationships and constraints
            ConfigureUserEntity(modelBuilder);
            ConfigureRoleEntity(modelBuilder);
            ConfigurePermissionEntity(modelBuilder);
            ConfigureUserRoleEntity(modelBuilder);
            ConfigureRolePermissionEntity(modelBuilder);
            ConfigureServiceEntity(modelBuilder);
            ConfigureOracleServiceEntity(modelBuilder);

            // Configure global query filters for soft delete
            ConfigureGlobalQueryFilters(modelBuilder);

            // Seed initial data
            SeedData(modelBuilder);
        }

        private static void ConfigureUserEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Email).IsRequired();
                entity.Property(e => e.FirstName).IsRequired();
                entity.Property(e => e.LastName).IsRequired();

                // Social authentication indexes
                entity.HasIndex(e => e.GoogleId).IsUnique().HasFilter("[GoogleId] IS NOT NULL");
                entity.HasIndex(e => e.FacebookId).IsUnique().HasFilter("[FacebookId] IS NOT NULL");

                // Token fields
                entity.Property(e => e.EmailConfirmationToken).HasMaxLength(500);
                entity.Property(e => e.PasswordResetToken).HasMaxLength(500);
                entity.Property(e => e.RefreshToken).HasMaxLength(500);
                entity.Property(e => e.GoogleId).HasMaxLength(100);
                entity.Property(e => e.FacebookId).HasMaxLength(100);
                entity.Property(e => e.ProfilePictureUrl).HasMaxLength(500);

                // Oracle-specific fields
                entity.Property(e => e.About).HasMaxLength(2000);
                entity.Property(e => e.Specializations).HasMaxLength(1000);
                entity.Property(e => e.Languages).HasMaxLength(500);
                entity.Property(e => e.HourlyRate).HasColumnType("decimal(18,2)");
            });
        }

        private static void ConfigureRoleEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Role>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).IsRequired();
            });
        }

        private static void ConfigurePermissionEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Permission>(entity =>
            {
                entity.HasIndex(e => e.Name).IsUnique();
                entity.Property(e => e.Name).IsRequired();
            });
        }

        private static void ConfigureUserRoleEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasIndex(e => new { e.UserId, e.RoleId }).IsUnique();
                
                entity.HasOne(ur => ur.User)
                    .WithMany(u => u.UserRoles)
                    .HasForeignKey(ur => ur.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(ur => ur.Role)
                    .WithMany(r => r.UserRoles)
                    .HasForeignKey(ur => ur.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private static void ConfigureRolePermissionEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<RolePermission>(entity =>
            {
                entity.HasIndex(e => new { e.RoleId, e.PermissionId }).IsUnique();
                
                entity.HasOne(rp => rp.Role)
                    .WithMany(r => r.RolePermissions)
                    .HasForeignKey(rp => rp.RoleId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(rp => rp.Permission)
                    .WithMany(p => p.RolePermissions)
                    .HasForeignKey(rp => rp.PermissionId)
                    .OnDelete(DeleteBehavior.Cascade);
            });
        }

        private static void ConfigureServiceEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Service>(entity =>
            {
                entity.Property(e => e.Name).IsRequired();
                entity.Property(e => e.Price).HasColumnType("decimal(18,2)");
                entity.Property(e => e.Category).IsRequired();
            });
        }

        private static void ConfigureOracleServiceEntity(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<OracleService>(entity =>
            {
                entity.HasIndex(e => new { e.UserId, e.ServiceId }).IsUnique();

                entity.HasOne(os => os.User)
                    .WithMany(u => u.OracleServices)
                    .HasForeignKey(os => os.UserId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(os => os.Service)
                    .WithMany(s => s.OracleServices)
                    .HasForeignKey(os => os.ServiceId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.Property(e => e.CustomPrice).HasColumnType("decimal(18,2)");
            });
        }

        private static void ConfigureGlobalQueryFilters(ModelBuilder modelBuilder)
        {
            // Apply global query filter for soft delete
            modelBuilder.Entity<User>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Role>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Permission>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<UserRole>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<RolePermission>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<Service>().HasQueryFilter(e => !e.IsDeleted);
            modelBuilder.Entity<OracleService>().HasQueryFilter(e => !e.IsDeleted);
        }

        private static void SeedData(ModelBuilder modelBuilder)
        {
            // Use a fixed date for seed data to avoid model changes
            var seedDate = new DateTime(2025, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // Seed default roles
            modelBuilder.Entity<Role>().HasData(
                new Role { Id = 1, Name = "Administrator", Description = "Full system access", CreatedAt = seedDate },
                new Role { Id = 2, Name = "User", Description = "Standard user access", CreatedAt = seedDate }
            );

            // Seed default permissions
            modelBuilder.Entity<Permission>().HasData(
                new Permission { Id = 1, Name = "Users.Read", Description = "Read user information", Category = "Users", CreatedAt = seedDate },
                new Permission { Id = 2, Name = "Users.Write", Description = "Create and update users", Category = "Users", CreatedAt = seedDate },
                new Permission { Id = 3, Name = "Users.Delete", Description = "Delete users", Category = "Users", CreatedAt = seedDate },
                new Permission { Id = 4, Name = "Roles.Read", Description = "Read role information", Category = "Roles", CreatedAt = seedDate },
                new Permission { Id = 5, Name = "Roles.Write", Description = "Create and update roles", Category = "Roles", CreatedAt = seedDate },
                new Permission { Id = 6, Name = "Roles.Delete", Description = "Delete roles", Category = "Roles", CreatedAt = seedDate }
            );

            // Seed role permissions (Administrator gets all permissions)
            modelBuilder.Entity<RolePermission>().HasData(
                new RolePermission { Id = 1, RoleId = 1, PermissionId = 1, CreatedAt = seedDate },
                new RolePermission { Id = 2, RoleId = 1, PermissionId = 2, CreatedAt = seedDate },
                new RolePermission { Id = 3, RoleId = 1, PermissionId = 3, CreatedAt = seedDate },
                new RolePermission { Id = 4, RoleId = 1, PermissionId = 4, CreatedAt = seedDate },
                new RolePermission { Id = 5, RoleId = 1, PermissionId = 5, CreatedAt = seedDate },
                new RolePermission { Id = 6, RoleId = 1, PermissionId = 6, CreatedAt = seedDate },
                // User role gets read permissions only
                new RolePermission { Id = 7, RoleId = 2, PermissionId = 1, CreatedAt = seedDate },
                new RolePermission { Id = 8, RoleId = 2, PermissionId = 4, CreatedAt = seedDate }
            );

            // Seed Oracle role
            modelBuilder.Entity<Role>().HasData(
                new Role { Id = 3, Name = "Oracle", Description = "Oracle service provider", CreatedAt = seedDate }
            );

            // Seed default services
            modelBuilder.Entity<Service>().HasData(
                new Service { Id = 1, Name = "Tarot Reading", Description = "Personal tarot card reading session", Price = 50.00m, DurationMinutes = 30, Category = "Divination", CreatedAt = seedDate },
                new Service { Id = 2, Name = "Astrology Consultation", Description = "Birth chart analysis and consultation", Price = 75.00m, DurationMinutes = 45, Category = "Astrology", CreatedAt = seedDate },
                new Service { Id = 3, Name = "Palm Reading", Description = "Palmistry and hand analysis", Price = 40.00m, DurationMinutes = 25, Category = "Divination", CreatedAt = seedDate },
                new Service { Id = 4, Name = "Crystal Healing", Description = "Energy healing with crystals", Price = 60.00m, DurationMinutes = 60, Category = "Healing", CreatedAt = seedDate },
                new Service { Id = 5, Name = "Spiritual Guidance", Description = "Life guidance and spiritual counseling", Price = 80.00m, DurationMinutes = 50, Category = "Counseling", CreatedAt = seedDate }
            );
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries<BaseEntity>();

            foreach (var entry in entries)
            {
                switch (entry.State)
                {
                    case EntityState.Added:
                        entry.Entity.CreatedAt = DateTime.UtcNow;
                        break;
                    case EntityState.Modified:
                        entry.Entity.UpdatedAt = DateTime.UtcNow;
                        break;
                }
            }
        }
    }
}
