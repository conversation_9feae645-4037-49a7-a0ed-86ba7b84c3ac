<!-- Dashboard Content -->
<div class="dashboard-container">
  <!-- Header Card -->
  <mat-card class="header-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>wb_sunny</mat-icon>
        Weather Forecast Dashboard
      </mat-card-title>
      <mat-card-subtitle>
        Real-time weather data from the server
      </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <p>This component demonstrates fetching data from the server using Angular Material components.</p>
      <button mat-raised-button color="primary" (click)="getForecasts()">
        <mat-icon>refresh</mat-icon>
        Refresh Data
      </button>
    </mat-card-content>
  </mat-card>

  <!-- Loading Spinner -->
  <div *ngIf="!forecasts" class="loading-container">
    <mat-card>
      <mat-card-content>
        <div class="loading-content">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading weather data...</p>
          <p><em>Please refresh once the ASP.NET backend has started.</em></p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Weather Data Table -->
  <mat-card *ngIf="forecasts" class="data-card">
    <mat-card-header>
      <mat-card-title>
        <mat-icon>table_chart</mat-icon>
        Weather Forecast Data
      </mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="forecasts" class="weather-table">
          <!-- Date Column -->
          <ng-container matColumnDef="date">
            <th mat-header-cell *matHeaderCellDef>
              <mat-icon>calendar_today</mat-icon>
              Date
            </th>
            <td mat-cell *matCellDef="let forecast">{{ forecast.date | date:'short' }}</td>
          </ng-container>

          <!-- Temperature C Column -->
          <ng-container matColumnDef="temperatureC">
            <th mat-header-cell *matHeaderCellDef>
              <mat-icon>thermostat</mat-icon>
              Temp. (°C)
            </th>
            <td mat-cell *matCellDef="let forecast">
              <span class="temperature">{{ forecast.temperatureC }}°</span>
            </td>
          </ng-container>

          <!-- Temperature F Column -->
          <ng-container matColumnDef="temperatureF">
            <th mat-header-cell *matHeaderCellDef>
              <mat-icon>thermostat</mat-icon>
              Temp. (°F)
            </th>
            <td mat-cell *matCellDef="let forecast">
              <span class="temperature">{{ forecast.temperatureF }}°</span>
            </td>
          </ng-container>

          <!-- Summary Column -->
          <ng-container matColumnDef="summary">
            <th mat-header-cell *matHeaderCellDef>
              <mat-icon>description</mat-icon>
              Summary
            </th>
            <td mat-cell *matCellDef="let forecast">
              <span class="summary">{{ forecast.summary }}</span>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Material Design Demo -->
  <app-material-demo></app-material-demo>
</div>
