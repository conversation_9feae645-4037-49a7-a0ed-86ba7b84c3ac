import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil, forkJoin } from 'rxjs';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatDialog } from '@angular/material/dialog';
import { Title, Meta } from '@angular/platform-browser';

import { ProfileService } from '../../services/profile.service';
import { AuthService } from '../../../auth/services/auth.service';
import { UserProfile, ProfileAnalytics, BlogPost } from '../../models/profile.models';
// import { ContactDialogComponent } from '../contact-dialog/contact-dialog.component';

@Component({
  selector: 'app-profile-view',
  templateUrl: './profile-view.component.html',
  styleUrls: ['./profile-view.component.css']
})
export class ProfileViewComponent implements OnInit, OnDestroy {
  profile: UserProfile | null = null;
  analytics: ProfileAnalytics | null = null;
  blogPosts: BlogPost[] = [];

  isOwnProfile = false;
  isLoading = true;
  isAuthenticated = false;
  currentUserId: number | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private profileService: ProfileService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog,
    private titleService: Title,
    private metaService: Meta
  ) {}

  ngOnInit(): void {
    this.initializeComponent();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeComponent(): void {
    // Check authentication status
    this.authService.isAuthenticated$
      .pipe(takeUntil(this.destroy$))
      .subscribe(isAuth => {
        this.isAuthenticated = isAuth;
      });

    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(user => {
        this.currentUserId = user?.id || null;
      });

    // Get profile identifier from route
    this.route.params
      .pipe(takeUntil(this.destroy$))
      .subscribe(params => {
        const identifier = params['identifier']; // username or user-id
        if (identifier) {
          this.loadProfile(identifier);
        }
      });
  }

  private loadProfile(identifier: string): void {
    this.isLoading = true;

    const profileRequest = this.profileService.getProfile(identifier);
    const blogPostsRequest = this.profileService.getBlogPosts(parseInt(identifier) || 0);

    forkJoin({
      profile: profileRequest,
      blogPosts: blogPostsRequest
    }).pipe(takeUntil(this.destroy$))
    .subscribe({
      next: ({ profile, blogPosts }) => {
        this.profile = profile;
        this.blogPosts = blogPosts;
        this.isOwnProfile = this.currentUserId === profile.userId;

        // Load analytics if it's own profile
        if (this.isOwnProfile) {
          this.loadAnalytics();
        }

        // Record profile view if not own profile
        if (!this.isOwnProfile) {
          this.recordProfileView();
        }

        // Set SEO metadata
        this.setSEOMetadata();

        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading profile:', error);
        this.snackBar.open('Profile not found', 'Close', { duration: 5000 });
        this.router.navigate(['/']);
        this.isLoading = false;
      }
    });
  }

  private loadAnalytics(): void {
    if (!this.isOwnProfile) return;

    this.profileService.getProfileAnalytics()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (analytics) => {
          this.analytics = analytics;
        },
        error: (error) => {
          console.error('Error loading analytics:', error);
        }
      });
  }

  private recordProfileView(): void {
    if (!this.profile) return;

    this.profileService.recordProfileView({
      profileId: this.profile.id,
      viewerUserId: this.currentUserId || undefined,
      referrer: document.referrer,
      userAgent: navigator.userAgent
    }).subscribe({
      error: (error) => {
        console.error('Error recording profile view:', error);
      }
    });
  }

  private setSEOMetadata(): void {
    if (!this.profile) return;

    const title = `${this.profile.firstName} ${this.profile.lastName} - ${this.profile.professionalTitle || 'Professional Profile'}`;
    const description = this.profile.summary || `View ${this.profile.firstName} ${this.profile.lastName}'s professional profile, experience, and portfolio.`;
    const imageUrl = this.profile.profilePhotoUrl || '/assets/images/default-profile.jpg';
    const profileUrl = `${window.location.origin}/profile/${this.profile.slug}`;

    // Set page title
    this.titleService.setTitle(title);

    // Set meta tags
    this.metaService.updateTag({ name: 'description', content: description });
    this.metaService.updateTag({ property: 'og:title', content: title });
    this.metaService.updateTag({ property: 'og:description', content: description });
    this.metaService.updateTag({ property: 'og:image', content: imageUrl });
    this.metaService.updateTag({ property: 'og:url', content: profileUrl });
    this.metaService.updateTag({ property: 'og:type', content: 'profile' });
    this.metaService.updateTag({ name: 'twitter:card', content: 'summary_large_image' });
    this.metaService.updateTag({ name: 'twitter:title', content: title });
    this.metaService.updateTag({ name: 'twitter:description', content: description });
    this.metaService.updateTag({ name: 'twitter:image', content: imageUrl });
  }

  // Action Methods
  onContactClick(): void {
    if (!this.profile) return;

    // TODO: Implement contact dialog
    // const dialogRef = this.dialog.open(ContactDialogComponent, {
    //   width: '500px',
    //   data: {
    //     profile: this.profile,
    //     isAuthenticated: this.isAuthenticated
    //   }
    // });

    // dialogRef.afterClosed().subscribe(result => {
    //   if (result) {
    //     this.snackBar.open('Message sent successfully!', 'Close', { duration: 3000 });
    //   }
    // });

    // Temporary implementation
    this.snackBar.open('Contact feature coming soon!', 'Close', { duration: 2000 });
  }

  onEditProfile(): void {
    this.router.navigate(['/profile/edit']);
  }

  onShareProfile(): void {
    if (!this.profile) return;

    if (navigator.share) {
      navigator.share({
        title: `${this.profile.firstName} ${this.profile.lastName} - Professional Profile`,
        text: this.profile.summary || 'Check out this professional profile',
        url: window.location.href
      }).catch(console.error);
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href).then(() => {
        this.snackBar.open('Profile URL copied to clipboard!', 'Close', { duration: 3000 });
      });
    }
  }

  onEndorseSkill(skillId: number): void {
    if (!this.isAuthenticated || !this.profile || this.isOwnProfile) return;

    this.profileService.endorseSkill({
      skillId: skillId,
      endorsedUserId: this.profile.userId
    }).subscribe({
      next: () => {
        this.snackBar.open('Skill endorsed!', 'Close', { duration: 2000 });
        // Refresh profile to update endorsement count
        this.loadProfile(this.profile!.slug);
      },
      error: (error) => {
        console.error('Error endorsing skill:', error);
        this.snackBar.open('Failed to endorse skill', 'Close', { duration: 3000 });
      }
    });
  }

  onFollowUser(): void {
    // TODO: Implement follow functionality
    this.snackBar.open('Follow feature coming soon!', 'Close', { duration: 2000 });
  }

  // Utility Methods
  getProfileCompletionColor(): string {
    if (!this.profile) return 'warn';

    const percentage = this.profile.profileCompletionPercentage;
    if (percentage >= 80) return 'primary';
    if (percentage >= 50) return 'accent';
    return 'warn';
  }

  getExperienceYears(): number {
    if (!this.profile?.experiences.length) return 0;

    const totalMonths = this.profile.experiences.reduce((total, exp) => {
      const start = new Date(exp.startDate);
      const end = exp.endDate ? new Date(exp.endDate) : new Date();
      const months = (end.getFullYear() - start.getFullYear()) * 12 + (end.getMonth() - start.getMonth());
      return total + months;
    }, 0);

    return Math.floor(totalMonths / 12);
  }

  formatDate(date: Date | string): string {
    return new Date(date).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    });
  }

  getSkillsByCategory(): { [category: string]: any[] } {
    if (!this.profile?.skills.length) return {};

    return this.profile.skills.reduce((acc, skill) => {
      const category = skill.category || 'Other';
      if (!acc[category]) acc[category] = [];
      acc[category].push(skill);
      return acc;
    }, {} as { [category: string]: any[] });
  }
}
