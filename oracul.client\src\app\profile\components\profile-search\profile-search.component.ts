import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { PageEvent } from '@angular/material/paginator';
import { ProfileService } from '../../services/profile.service';
import { UserProfile, ProfileSearchFilters, ProfileSearchResult } from '../../models/profile.models';

@Component({
  selector: 'app-profile-search',
  template: `
    <div class="search-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>search</mat-icon>
            Find Oracle Professionals
          </mat-card-title>
          <mat-card-subtitle>
            Search through our directory of spiritual advisors and service providers. Try searching by location (e.g., "California", "Sedona") or skills (e.g., "tarot", "astrology", "healing").
          </mat-card-subtitle>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="searchForm" (ngSubmit)="onSearch()">
            <div class="search-fields">
              <mat-form-field appearance="outline">
                <mat-label>Location</mat-label>
                <input matInput formControlName="location" placeholder="City, State, Country">
                <mat-icon matSuffix>location_on</mat-icon>
              </mat-form-field>
              
              <mat-form-field appearance="outline">
                <mat-label>Skills</mat-label>
                <input matInput formControlName="skills" placeholder="tarot, astrology, healing, etc.">
                <mat-icon matSuffix>psychology</mat-icon>
              </mat-form-field>

              <mat-form-field appearance="outline">
                <mat-label>Sort By</mat-label>
                <mat-select formControlName="sortBy">
                  <mat-option value="relevance">Relevance</mat-option>
                  <mat-option value="views">Most Viewed</mat-option>
                  <mat-option value="alphabetical">Name (A-Z)</mat-option>
                  <mat-option value="recent">Recently Updated</mat-option>
                </mat-select>
                <mat-icon matSuffix>sort</mat-icon>
              </mat-form-field>
              
              <button mat-raised-button color="primary" type="submit" [disabled]="isLoading">
                <mat-spinner *ngIf="isLoading" diameter="20"></mat-spinner>
                <mat-icon *ngIf="!isLoading">search</mat-icon>
                {{ isLoading ? 'Searching...' : 'Search' }}
              </button>

              <button mat-stroked-button type="button" (click)="clearSearch()" [disabled]="isLoading">
                <mat-icon>clear</mat-icon>
                Clear
              </button>
            </div>

            <!-- Quick Search Suggestions -->
            <div class="search-suggestions">
              <span class="suggestions-label">Try searching for:</span>
              <button mat-button class="suggestion-chip" (click)="quickSearch('California')">California</button>
              <button mat-button class="suggestion-chip" (click)="quickSearch('tarot')">Tarot Reading</button>
              <button mat-button class="suggestion-chip" (click)="quickSearch('astrology')">Astrology</button>
              <button mat-button class="suggestion-chip" (click)="quickSearch('healing')">Crystal Healing</button>
              <button mat-button class="suggestion-chip" (click)="quickSearch('meditation')">Meditation</button>
            </div>
          </form>
          
          <div class="search-results" *ngIf="searchResults">
            <div class="results-header">
              <h3>{{ searchResults.totalCount }} professional{{ searchResults.totalCount !== 1 ? 's' : '' }} found</h3>
              <div class="view-options">
                <mat-button-toggle-group [(value)]="viewMode" class="view-toggle">
                  <mat-button-toggle value="grid">
                    <mat-icon>grid_view</mat-icon>
                  </mat-button-toggle>
                  <mat-button-toggle value="list">
                    <mat-icon>view_list</mat-icon>
                  </mat-button-toggle>
                </mat-button-toggle-group>
              </div>
            </div>

            <div class="profiles-container" [class.grid-view]="viewMode === 'grid'" [class.list-view]="viewMode === 'list'" *ngIf="searchResults.profiles.length > 0">
              <app-profile-card
                *ngFor="let profile of searchResults.profiles"
                [profile]="profile"
                [compact]="viewMode === 'grid'"
                (contactClicked)="onContactProfile($event)"
                (profileClicked)="onProfileClicked($event)">
              </app-profile-card>
            </div>

            <!-- No Results Message -->
            <div class="no-results" *ngIf="searchResults.profiles.length === 0">
              <mat-icon>search_off</mat-icon>
              <h3>No professionals found</h3>
              <p>Try adjusting your search criteria or browse all available professionals.</p>
              <button mat-raised-button color="primary" (click)="clearSearch()">
                <mat-icon>refresh</mat-icon>
                Show All Professionals
              </button>
            </div>

            <!-- Pagination -->
            <div class="pagination-container" *ngIf="searchResults.totalPages > 1">
              <mat-paginator
                [length]="searchResults.totalCount"
                [pageSize]="pageSize"
                [pageSizeOptions]="[10, 20, 50]"
                [pageIndex]="currentPage - 1"
                (page)="onPageChange($event)"
                showFirstLastButtons>
              </mat-paginator>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .search-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .search-fields {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .search-fields mat-form-field {
      flex: 1;
      min-width: 200px;
    }

    .search-suggestions {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-top: 12px;
      flex-wrap: wrap;
    }

    .suggestions-label {
      font-size: 0.9rem;
      color: #666;
      margin-right: 8px;
    }

    .suggestion-chip {
      font-size: 0.8rem;
      height: 32px;
      border-radius: 16px;
      background-color: #f5f5f5;
      color: #673ab7;
      border: 1px solid #e0e0e0;
    }

    .suggestion-chip:hover {
      background-color: #e3f2fd;
      border-color: #673ab7;
    }

    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 24px 0 16px 0;
      flex-wrap: wrap;
      gap: 16px;
    }

    .results-header h3 {
      margin: 0;
      color: #333;
      font-weight: 500;
    }

    .view-options {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .view-toggle {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
    }

    .profiles-container.grid-view {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      margin-bottom: 24px;
    }

    .profiles-container.list-view {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 24px;
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 32px;
    }

    .no-results {
      text-align: center;
      padding: 48px 24px;
      color: #666;
    }

    .no-results mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #ccc;
      margin-bottom: 16px;
    }

    .no-results h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    .no-results p {
      margin: 0 0 24px 0;
      font-size: 0.95rem;
    }

    @media (max-width: 768px) {
      .search-container {
        padding: 16px;
      }

      .search-fields {
        flex-direction: column;
        gap: 12px;
      }

      .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .profiles-container.grid-view {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }

    @media (max-width: 1024px) {
      .profiles-container.grid-view {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ProfileSearchComponent implements OnInit {
  searchForm: FormGroup;
  searchResults: ProfileSearchResult | null = null;
  isLoading = false;
  viewMode: 'grid' | 'list' = 'grid';
  currentPage = 1;
  pageSize = 20;

  constructor(
    private formBuilder: FormBuilder,
    private profileService: ProfileService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.searchForm = this.formBuilder.group({
      location: [''],
      skills: [''],
      experience: [''],
      sortBy: ['relevance']
    });
  }

  ngOnInit(): void {
    // Load dummy data for demonstration
    this.loadDummyData();
  }

  onSearch(): void {
    this.currentPage = 1;
    this.performSearch();
  }

  clearSearch(): void {
    this.searchForm.reset({
      location: '',
      skills: '',
      experience: '',
      sortBy: 'relevance'
    });
    this.loadDummyData(); // Reset to show all profiles
  }

  quickSearch(term: string): void {
    // Determine if it's a location or skill search
    const locations = ['california', 'arizona', 'sedona', 'los angeles', 'santa fe', 'new orleans', 'portland', 'asheville', 'boulder', 'san francisco'];
    const isLocation = locations.some(loc => term.toLowerCase().includes(loc));

    if (isLocation) {
      this.searchForm.patchValue({ location: term, skills: '' });
    } else {
      this.searchForm.patchValue({ skills: term, location: '' });
    }

    this.onSearch();
  }

  private performSearch(): void {
    this.isLoading = true;
    const formValue = this.searchForm.value;

    // Simulate search delay for realistic experience
    setTimeout(() => {
      const allProfiles = this.createDummyProfiles();
      let filteredProfiles = [...allProfiles];

      // Apply location filter
      if (formValue.location && formValue.location.trim()) {
        const locationFilter = formValue.location.toLowerCase();
        filteredProfiles = filteredProfiles.filter(profile =>
          profile.location?.displayLocation?.toLowerCase().includes(locationFilter) ||
          profile.location?.city?.toLowerCase().includes(locationFilter) ||
          profile.location?.state?.toLowerCase().includes(locationFilter)
        );
      }

      // Apply skills filter
      if (formValue.skills && formValue.skills.trim()) {
        const skillsFilter = formValue.skills.toLowerCase().split(',').map((s: string) => s.trim());
        filteredProfiles = filteredProfiles.filter(profile =>
          skillsFilter.some((skill: string) =>
            profile.skills.some(profileSkill =>
              profileSkill.name.toLowerCase().includes(skill)
            ) ||
            profile.professionalTitle?.toLowerCase().includes(skill) ||
            profile.summary?.toLowerCase().includes(skill)
          )
        );
      }

      // Apply sorting
      switch (formValue.sortBy) {
        case 'views':
          filteredProfiles.sort((a, b) => b.profileViews - a.profileViews);
          break;
        case 'alphabetical':
          filteredProfiles.sort((a, b) => a.firstName.localeCompare(b.firstName));
          break;
        case 'recent':
          filteredProfiles.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
          break;
        default: // relevance
          // Keep original order for relevance
          break;
      }

      this.searchResults = {
        profiles: filteredProfiles,
        totalCount: filteredProfiles.length,
        currentPage: this.currentPage,
        totalPages: Math.ceil(filteredProfiles.length / this.pageSize),
        pageSize: this.pageSize
      };

      this.isLoading = false;
    }, 500); // 500ms delay to simulate API call
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.performSearch();
  }

  onContactProfile(profile: UserProfile): void {
    // Handle contact action - could open a dialog or navigate to contact form
    if (profile.contactInfo?.email) {
      window.location.href = `mailto:${profile.contactInfo.email}`;
    } else {
      this.snackBar.open('Contact information not available', 'Close', {
        duration: 3000
      });
    }
  }

  onProfileClicked(profile: UserProfile): void {
    // All profiles lead to luna-starweaver for demo purposes
    this.router.navigate(['/oracle', 1]); // Assuming luna-starweaver has ID 1
  }

  private loadDummyData(): void {
    // Create dummy search results with various oracle profiles
    this.searchResults = {
      profiles: this.createDummyProfiles(),
      totalCount: 8,
      currentPage: 1,
      totalPages: 1,
      pageSize: 20
    };
  }

  private createDummyProfiles(): UserProfile[] {
    const baseProfile = {
      userId: 1,
      username: 'luna-starweaver',
      slug: 'luna-starweaver',
      isPublic: true,
      profileCompletionPercentage: 95,
      contactInfo: {
        email: '<EMAIL>',
        isEmailPublic: true,
        website: 'https://starweaver.com',
        phoneNumbers: [
          {
            number: '+****************',
            type: 'business',
            isPublic: true,
            isPrimary: true
          }
        ]
      },
      experiences: [
        {
          company: 'Cosmic Wisdom Center',
          position: 'Senior Astrologer',
          startDate: new Date('2018-01-01'),
          isCurrent: true,
          description: 'Leading astrologer providing personalized readings',
          location: 'Sedona, Arizona'
        }
      ],
      portfolioItems: [
        {
          title: 'Cosmic Birth Chart Analysis',
          description: 'Comprehensive natal chart reading service',
          imageUrls: ['https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'],
          technologies: ['Astrology', 'Spiritual Guidance'],
          completedAt: new Date('2023-01-01'),
          category: 'Astrology Services'
        }
      ],
      blogPosts: [],
      achievements: [],
      certifications: [],
      socialLinks: [],
      profileViews: 1247,
      createdAt: new Date('2020-01-01'),
      updatedAt: new Date('2023-12-01')
    };

    return [
      {
        ...baseProfile,
        id: 1,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400',
        firstName: 'Luna',
        lastName: 'Starweaver',
        professionalTitle: 'Professional Astrologer & Cosmic Guide',
        headline: 'Illuminating your path through the wisdom of the stars',
        location: {
          city: 'Sedona',
          state: 'Arizona',
          country: 'USA',
          displayLocation: 'Sedona, Arizona'
        },
        summary: 'With over 15 years of experience in astrology and cosmic guidance, I help individuals discover their true path through personalized readings and spiritual counseling.',
        skills: [
          { name: 'Natal Chart Reading', endorsements: 45, proficiencyLevel: 'expert' },
          { name: 'Tarot Reading', endorsements: 38, proficiencyLevel: 'expert' },
          { name: 'Spiritual Counseling', endorsements: 32, proficiencyLevel: 'advanced' },
          { name: 'Crystal Healing', endorsements: 28, proficiencyLevel: 'advanced' }
        ]
      },
      {
        ...baseProfile,
        id: 2,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400',
        firstName: 'Marcus',
        lastName: 'Celestial',
        professionalTitle: 'Birth Chart Specialist & Cosmic Counselor',
        headline: 'Unlocking the secrets of your cosmic blueprint',
        location: {
          city: 'Los Angeles',
          state: 'California',
          country: 'USA',
          displayLocation: 'Los Angeles, California'
        },
        summary: 'Specialized in birth chart analysis and cosmic counseling with 12 years of experience. I help clients understand their life purpose and navigate challenges.',
        skills: [
          { name: 'Birth Chart Analysis', endorsements: 52, proficiencyLevel: 'expert' },
          { name: 'Compatibility Reading', endorsements: 41, proficiencyLevel: 'expert' },
          { name: 'Life Transitions', endorsements: 35, proficiencyLevel: 'advanced' }
        ],
        profileViews: 892
      },
      {
        ...baseProfile,
        id: 3,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400',
        firstName: 'Sage',
        lastName: 'Moonchild',
        professionalTitle: 'Crystal Healer & Energy Worker',
        headline: 'Healing through the power of crystals and energy work',
        location: {
          city: 'Santa Fe',
          state: 'New Mexico',
          country: 'USA',
          displayLocation: 'Santa Fe, New Mexico'
        },
        summary: 'Crystal healer and energy worker dedicated to helping others find balance and healing. I combine crystal therapy with meditation and chakra balancing.',
        skills: [
          { name: 'Crystal Healing', endorsements: 38, proficiencyLevel: 'expert' },
          { name: 'Energy Work', endorsements: 34, proficiencyLevel: 'expert' },
          { name: 'Chakra Balancing', endorsements: 29, proficiencyLevel: 'advanced' },
          { name: 'Meditation', endorsements: 25, proficiencyLevel: 'intermediate' }
        ],
        profileViews: 654
      },
      {
        ...baseProfile,
        id: 4,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
        firstName: 'River',
        lastName: 'Palmistry',
        professionalTitle: 'Third-Generation Palm Reader',
        headline: 'Reading the stories written in your hands',
        location: {
          city: 'New Orleans',
          state: 'Louisiana',
          country: 'USA',
          displayLocation: 'New Orleans, Louisiana'
        },
        summary: 'Third-generation palm reader with a gift for seeing life\'s path through the lines of your hands. I provide insights into personality, relationships, and future possibilities.',
        skills: [
          { name: 'Palm Reading', endorsements: 42, proficiencyLevel: 'expert' },
          { name: 'Life Path Analysis', endorsements: 36, proficiencyLevel: 'expert' },
          { name: 'Personality Reading', endorsements: 31, proficiencyLevel: 'advanced' }
        ],
        profileViews: 789
      },
      {
        ...baseProfile,
        id: 5,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400',
        firstName: 'Aurora',
        lastName: 'Wisdom',
        professionalTitle: 'Spiritual Counselor & Life Coach',
        headline: 'Guiding souls toward their highest potential',
        location: {
          city: 'Portland',
          state: 'Oregon',
          country: 'USA',
          displayLocation: 'Portland, Oregon'
        },
        summary: 'Spiritual counselor and life coach helping people find their purpose and overcome challenges. I offer compassionate guidance for all of life\'s journeys.',
        skills: [
          { name: 'Spiritual Counseling', endorsements: 48, proficiencyLevel: 'expert' },
          { name: 'Life Coaching', endorsements: 44, proficiencyLevel: 'expert' },
          { name: 'Purpose Discovery', endorsements: 37, proficiencyLevel: 'advanced' }
        ],
        profileViews: 1156
      },
      {
        ...baseProfile,
        id: 6,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1489424731084-a5d8b219a5bb?w=400',
        firstName: 'Mystic',
        lastName: 'Rose',
        professionalTitle: 'Intuitive Reader & Dream Interpreter',
        headline: 'Unlocking the messages from your subconscious mind',
        location: {
          city: 'Asheville',
          state: 'North Carolina',
          country: 'USA',
          displayLocation: 'Asheville, North Carolina'
        },
        summary: 'Intuitive reader specializing in dream interpretation and subconscious guidance. I help clients understand the deeper meanings behind their dreams and intuitive insights.',
        skills: [
          { name: 'Dream Interpretation', endorsements: 33, proficiencyLevel: 'expert' },
          { name: 'Intuitive Reading', endorsements: 29, proficiencyLevel: 'expert' },
          { name: 'Subconscious Work', endorsements: 26, proficiencyLevel: 'advanced' }
        ],
        profileViews: 567
      },
      {
        ...baseProfile,
        id: 7,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1531746020798-e6953c6e8e04?w=400',
        firstName: 'Cosmic',
        lastName: 'Dawn',
        professionalTitle: 'Numerologist & Sacred Geometry Expert',
        headline: 'Discovering your life\'s blueprint through numbers',
        location: {
          city: 'Boulder',
          state: 'Colorado',
          country: 'USA',
          displayLocation: 'Boulder, Colorado'
        },
        summary: 'Numerologist and sacred geometry expert who reveals the hidden patterns and meanings in your life through the power of numbers and geometric principles.',
        skills: [
          { name: 'Numerology', endorsements: 41, proficiencyLevel: 'expert' },
          { name: 'Sacred Geometry', endorsements: 35, proficiencyLevel: 'expert' },
          { name: 'Life Path Numbers', endorsements: 32, proficiencyLevel: 'advanced' }
        ],
        profileViews: 723
      },
      {
        ...baseProfile,
        id: 8,
        profilePhotoUrl: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=400',
        firstName: 'Serenity',
        lastName: 'Moon',
        professionalTitle: 'Meditation Guide & Mindfulness Coach',
        headline: 'Finding peace and clarity through mindful practice',
        location: {
          city: 'San Francisco',
          state: 'California',
          country: 'USA',
          displayLocation: 'San Francisco, California'
        },
        summary: 'Meditation guide and mindfulness coach dedicated to helping others find inner peace and mental clarity through various meditation techniques and mindfulness practices.',
        skills: [
          { name: 'Meditation Guidance', endorsements: 46, proficiencyLevel: 'expert' },
          { name: 'Mindfulness Coaching', endorsements: 39, proficiencyLevel: 'expert' },
          { name: 'Stress Relief', endorsements: 34, proficiencyLevel: 'advanced' },
          { name: 'Inner Peace', endorsements: 28, proficiencyLevel: 'advanced' }
        ],
        profileViews: 934
      }
    ];
  }
}
