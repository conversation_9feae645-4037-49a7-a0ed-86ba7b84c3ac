import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ProfileService } from '../../services/profile.service';
import { UserProfile, ProfileSearchFilters, ProfileSearchResult } from '../../models/profile.models';

@Component({
  selector: 'app-profile-search',
  template: `
    <div class="search-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>search</mat-icon>
            Find Professionals
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="searchForm" (ngSubmit)="onSearch()">
            <div class="search-fields">
              <mat-form-field appearance="outline">
                <mat-label>Location</mat-label>
                <input matInput formControlName="location" placeholder="City, State, Country">
                <mat-icon matSuffix>location_on</mat-icon>
              </mat-form-field>
              
              <mat-form-field appearance="outline">
                <mat-label>Skills</mat-label>
                <input matInput formControlName="skills" placeholder="JavaScript, Angular, etc.">
                <mat-icon matSuffix>psychology</mat-icon>
              </mat-form-field>
              
              <button mat-raised-button color="primary" type="submit">
                <mat-icon>search</mat-icon>
                Search
              </button>
            </div>
          </form>
          
          <div class="search-results" *ngIf="searchResults">
            <h3>{{ searchResults.totalCount }} professionals found</h3>
            <div class="profiles-grid">
              <mat-card class="profile-card" *ngFor="let profile of searchResults.profiles">
                <div class="profile-preview">
                  <img [src]="profile.profilePhotoUrl || '/assets/images/default-avatar.png'" 
                       [alt]="profile.firstName + ' ' + profile.lastName">
                  <div class="profile-info">
                    <h4>{{ profile.firstName }} {{ profile.lastName }}</h4>
                    <p>{{ profile.professionalTitle }}</p>
                    <p class="location">{{ profile.location?.displayLocation }}</p>
                  </div>
                  <button mat-button color="primary" [routerLink]="['/profile', profile.slug]">
                    View Profile
                  </button>
                </div>
              </mat-card>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .search-container { padding: 20px; }
    .search-fields { display: flex; gap: 16px; align-items: center; flex-wrap: wrap; }
    .profiles-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 16px; margin-top: 20px; }
    .profile-card { padding: 16px; }
    .profile-preview { display: flex; flex-direction: column; align-items: center; text-align: center; gap: 8px; }
    .profile-preview img { width: 80px; height: 80px; border-radius: 50%; object-fit: cover; }
    .location { color: var(--theme-text-secondary); font-size: 0.9rem; }
  `]
})
export class ProfileSearchComponent implements OnInit {
  searchForm: FormGroup;
  searchResults: ProfileSearchResult | null = null;
  isLoading = false;

  constructor(
    private formBuilder: FormBuilder,
    private profileService: ProfileService
  ) {
    this.searchForm = this.formBuilder.group({
      location: [''],
      skills: [''],
      experience: [''],
      sortBy: ['relevance']
    });
  }

  ngOnInit(): void {}

  onSearch(): void {
    this.isLoading = true;
    const formValue = this.searchForm.value;
    
    const filters: ProfileSearchFilters = {
      location: formValue.location || undefined,
      skills: formValue.skills ? formValue.skills.split(',').map((s: string) => s.trim()) : undefined,
      experience: formValue.experience || undefined,
      sortBy: formValue.sortBy
    };

    this.profileService.searchProfiles(filters).subscribe({
      next: (results) => {
        this.searchResults = results;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Search error:', error);
        this.isLoading = false;
      }
    });
  }
}
