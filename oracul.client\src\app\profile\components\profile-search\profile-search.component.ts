import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { PageEvent } from '@angular/material/paginator';
import { ProfileService } from '../../services/profile.service';
import { UserProfile, ProfileSearchFilters, ProfileSearchResult, UserProfile } from '../../models/profile.models';

@Component({
  selector: 'app-profile-search',
  template: `
    <div class="search-container">
      <mat-card>
        <mat-card-header>
          <mat-card-title>
            <mat-icon>search</mat-icon>
            Find Professionals
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <form [formGroup]="searchForm" (ngSubmit)="onSearch()">
            <div class="search-fields">
              <mat-form-field appearance="outline">
                <mat-label>Location</mat-label>
                <input matInput formControlName="location" placeholder="City, State, Country">
                <mat-icon matSuffix>location_on</mat-icon>
              </mat-form-field>
              
              <mat-form-field appearance="outline">
                <mat-label>Skills</mat-label>
                <input matInput formControlName="skills" placeholder="JavaScript, Angular, etc.">
                <mat-icon matSuffix>psychology</mat-icon>
              </mat-form-field>
              
              <button mat-raised-button color="primary" type="submit">
                <mat-icon>search</mat-icon>
                Search
              </button>
            </div>
          </form>
          
          <div class="search-results" *ngIf="searchResults">
            <div class="results-header">
              <h3>{{ searchResults.totalCount }} professional{{ searchResults.totalCount !== 1 ? 's' : '' }} found</h3>
              <div class="view-options">
                <mat-button-toggle-group [(value)]="viewMode" class="view-toggle">
                  <mat-button-toggle value="grid">
                    <mat-icon>grid_view</mat-icon>
                  </mat-button-toggle>
                  <mat-button-toggle value="list">
                    <mat-icon>view_list</mat-icon>
                  </mat-button-toggle>
                </mat-button-toggle-group>
              </div>
            </div>

            <div class="profiles-container" [class.grid-view]="viewMode === 'grid'" [class.list-view]="viewMode === 'list'">
              <app-profile-card
                *ngFor="let profile of searchResults.profiles"
                [profile]="profile"
                [compact]="viewMode === 'grid'"
                (contactClicked)="onContactProfile($event)"
                (profileClicked)="onProfileClicked($event)">
              </app-profile-card>
            </div>

            <!-- Pagination -->
            <div class="pagination-container" *ngIf="searchResults.totalPages > 1">
              <mat-paginator
                [length]="searchResults.totalCount"
                [pageSize]="pageSize"
                [pageSizeOptions]="[10, 20, 50]"
                [pageIndex]="currentPage - 1"
                (page)="onPageChange($event)"
                showFirstLastButtons>
              </mat-paginator>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .search-container {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .search-fields {
      display: flex;
      gap: 16px;
      margin-bottom: 16px;
      flex-wrap: wrap;
    }

    .search-fields mat-form-field {
      flex: 1;
      min-width: 200px;
    }

    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin: 24px 0 16px 0;
      flex-wrap: wrap;
      gap: 16px;
    }

    .results-header h3 {
      margin: 0;
      color: #333;
      font-weight: 500;
    }

    .view-options {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .view-toggle {
      border: 1px solid #e0e0e0;
      border-radius: 6px;
    }

    .profiles-container.grid-view {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 20px;
      margin-bottom: 24px;
    }

    .profiles-container.list-view {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 24px;
    }

    .pagination-container {
      display: flex;
      justify-content: center;
      margin-top: 32px;
    }

    @media (max-width: 768px) {
      .search-container {
        padding: 16px;
      }

      .search-fields {
        flex-direction: column;
        gap: 12px;
      }

      .results-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .profiles-container.grid-view {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }

    @media (max-width: 1024px) {
      .profiles-container.grid-view {
        grid-template-columns: 1fr;
      }
    }
  `]
})
export class ProfileSearchComponent implements OnInit {
  searchForm: FormGroup;
  searchResults: ProfileSearchResult | null = null;
  isLoading = false;
  viewMode: 'grid' | 'list' = 'grid';
  currentPage = 1;
  pageSize = 20;

  constructor(
    private formBuilder: FormBuilder,
    private profileService: ProfileService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.searchForm = this.formBuilder.group({
      location: [''],
      skills: [''],
      experience: [''],
      sortBy: ['relevance']
    });
  }

  ngOnInit(): void {}

  onSearch(): void {
    this.currentPage = 1;
    this.performSearch();
  }

  private performSearch(): void {
    this.isLoading = true;
    const formValue = this.searchForm.value;

    const filters: ProfileSearchFilters = {
      location: formValue.location || undefined,
      skills: formValue.skills ? formValue.skills.split(',').map((s: string) => s.trim()) : undefined,
      experience: formValue.experience || undefined,
      sortBy: formValue.sortBy
    };

    this.profileService.searchProfiles(filters, this.currentPage, this.pageSize).subscribe({
      next: (results) => {
        this.searchResults = results;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Search error:', error);
        this.snackBar.open('Error searching profiles. Please try again.', 'Close', {
          duration: 5000
        });
        this.isLoading = false;
      }
    });
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex + 1;
    this.pageSize = event.pageSize;
    this.performSearch();
  }

  onContactProfile(profile: UserProfile): void {
    // Handle contact action - could open a dialog or navigate to contact form
    if (profile.contactInfo?.email) {
      window.location.href = `mailto:${profile.contactInfo.email}`;
    } else {
      this.snackBar.open('Contact information not available', 'Close', {
        duration: 3000
      });
    }
  }

  onProfileClicked(profile: UserProfile): void {
    this.router.navigate(['/profile', profile.slug]);
  }
}
