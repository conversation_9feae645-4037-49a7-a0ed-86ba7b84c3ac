using Oracul.Data.Models;

namespace Oracul.Data.Interfaces
{
    /// <summary>
    /// Repository interface for OracleService entity operations
    /// </summary>
    public interface IOracleServiceRepository : IRepository<OracleService>
    {
        /// <summary>
        /// Get services offered by a specific oracle
        /// </summary>
        Task<IEnumerable<OracleService>> GetServicesByOracleIdAsync(int oracleId);

        /// <summary>
        /// Get oracles offering a specific service
        /// </summary>
        Task<IEnumerable<OracleService>> GetOraclesByServiceIdAsync(int serviceId);

        /// <summary>
        /// Get oracle service with full details (including User and Service)
        /// </summary>
        Task<OracleService?> GetOracleServiceWithDetailsAsync(int oracleId, int serviceId);
    }
}
