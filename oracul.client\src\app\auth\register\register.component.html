<div class="register-container">
  <mat-card class="register-card">
    <mat-card-header class="register-header">
      <mat-card-title>
        <mat-icon class="register-icon">person_add</mat-icon>
        Create Your Account
      </mat-card-title>
      <mat-card-subtitle>
        Join <PERSON> and start your journey
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
        <!-- Name Fields Row -->
        <div class="name-row">
          <mat-form-field appearance="outline" class="half-width">
            <mat-label>First Name</mat-label>
            <input
              matInput
              type="text"
              formControlName="firstName"
              placeholder="Enter first name"
              autocomplete="given-name">
            <mat-icon matSuffix>person</mat-icon>
            <mat-error *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched">
              {{ getFirstNameErrorMessage() }}
            </mat-error>
          </mat-form-field>

          <mat-form-field appearance="outline" class="half-width">
            <mat-label>Last Name</mat-label>
            <input
              matInput
              type="text"
              formControlName="lastName"
              placeholder="Enter last name"
              autocomplete="family-name">
            <mat-icon matSuffix>person</mat-icon>
            <mat-error *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched">
              {{ getLastNameErrorMessage() }}
            </mat-error>
          </mat-form-field>
        </div>

        <!-- Email Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email Address</mat-label>
          <input
            matInput
            type="email"
            formControlName="email"
            placeholder="Enter your email"
            autocomplete="email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched">
            {{ getEmailErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Phone Number Field (Optional) -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Phone Number (Optional)</mat-label>
          <input
            matInput
            type="tel"
            formControlName="phoneNumber"
            placeholder="Enter your phone number"
            autocomplete="tel">
          <mat-icon matSuffix>phone</mat-icon>
        </mat-form-field>

        <!-- Password Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Password</mat-label>
          <input
            matInput
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="password"
            placeholder="Create a password"
            autocomplete="new-password">
          <button
            mat-icon-button
            matSuffix
            type="button"
            (click)="hidePassword = !hidePassword"
            [attr.aria-label]="'Hide password'"
            [attr.aria-pressed]="hidePassword">
            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched">
            {{ getPasswordErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Confirm Password Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Confirm Password</mat-label>
          <input
            matInput
            [type]="hideConfirmPassword ? 'password' : 'text'"
            formControlName="confirmPassword"
            placeholder="Confirm your password"
            autocomplete="new-password">
          <button
            mat-icon-button
            matSuffix
            type="button"
            (click)="hideConfirmPassword = !hideConfirmPassword"
            [attr.aria-label]="'Hide password'"
            [attr.aria-pressed]="hideConfirmPassword">
            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error *ngIf="(registerForm.get('confirmPassword')?.invalid && registerForm.get('confirmPassword')?.touched) || registerForm.hasError('passwordMismatch')">
            {{ getConfirmPasswordErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Terms and Conditions -->
        <div class="terms-section">
          <mat-checkbox formControlName="acceptTerms" color="primary">
            I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
          </mat-checkbox>
          <mat-error *ngIf="registerForm.get('acceptTerms')?.invalid && registerForm.get('acceptTerms')?.touched">
            You must accept the terms and conditions
          </mat-error>
        </div>

        <!-- Register Button -->
        <button
          mat-raised-button
          color="primary"
          type="submit"
          class="full-width register-button"
          [disabled]="isLoading">
          <mat-icon *ngIf="isLoading">
            <mat-spinner diameter="20"></mat-spinner>
          </mat-icon>
          <mat-icon *ngIf="!isLoading">person_add</mat-icon>
          {{ isLoading ? 'Creating Account...' : 'Create Account' }}
        </button>

        <!-- Divider -->
        <div class="divider-container">
          <mat-divider></mat-divider>
          <span class="divider-text">or</span>
          <mat-divider></mat-divider>
        </div>

        <!-- OAuth Buttons -->
        <div class="oauth-buttons">
          <!-- Google Sign-Up Button -->
          <button
            mat-stroked-button
            type="button"
            class="full-width oauth-button google-button"
            [disabled]="isLoading"
            (click)="signUpWithGoogle()">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="oauth-icon">
            Sign up with Google
          </button>

          <!-- Facebook Sign-Up Button -->
          <button
            mat-stroked-button
            type="button"
            class="full-width oauth-button facebook-button"
            [disabled]="isLoading"
            (click)="signUpWithFacebook()">
            <mat-icon class="oauth-icon facebook-icon">facebook</mat-icon>
            Sign up with Facebook
          </button>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions class="register-actions">
      <mat-divider></mat-divider>

      <div class="login-section">
        <p class="login-text">Already have an account?</p>
        <button
          mat-stroked-button
          color="primary"
          type="button"
          (click)="navigateToLogin()"
          class="full-width">
          <mat-icon>login</mat-icon>
          Sign In
        </button>
      </div>
    </mat-card-actions>
  </mat-card>
</div>
