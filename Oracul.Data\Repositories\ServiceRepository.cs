using Microsoft.EntityFrameworkCore;
using Oracul.Data.Data;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;

namespace Oracul.Data.Repositories
{
    /// <summary>
    /// Repository implementation for Service entity operations
    /// </summary>
    public class ServiceRepository : Repository<Service>, IServiceRepository
    {
        public ServiceRepository(OraculDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Get services by category
        /// </summary>
        public async Task<IEnumerable<Service>> GetServicesByCategoryAsync(string category)
        {
            return await _context.Services
                .Where(s => s.Category == category && s.IsActive)
                .OrderBy(s => s.Name)
                .ToListAsync();
        }

        /// <summary>
        /// Get active services only
        /// </summary>
        public async Task<IEnumerable<Service>> GetActiveServicesAsync()
        {
            return await _context.Services
                .Where(s => s.IsActive)
                .OrderBy(s => s.Category)
                .ThenBy(s => s.Name)
                .ToListAsync();
        }
    }
}
