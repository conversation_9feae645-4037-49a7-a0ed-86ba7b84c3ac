import { Component, OnInit } from '@angular/core';
import { MatSnackBar } from '@angular/material/snack-bar';
import { ThemeService } from '../theme.service';
import { ThemeConfig } from '../theme.config';

@Component({
  selector: 'app-theme-selector',
  templateUrl: './theme-selector.component.html',
  styleUrls: ['./theme-selector.component.css']
})
export class ThemeSelectorComponent implements OnInit {
  availableThemes: ThemeConfig[] = [];
  currentTheme: ThemeConfig;

  constructor(
    private themeService: ThemeService,
    private snackBar: MatSnackBar
  ) {
    this.currentTheme = this.themeService.getCurrentTheme();
  }

  ngOnInit(): void {
    this.availableThemes = this.themeService.getAvailableThemes();

    this.themeService.currentTheme$.subscribe(theme => {
      this.currentTheme = theme;
    });
  }

  selectTheme(theme: ThemeConfig): void {
    console.log('Selecting theme:', theme.name);
    this.themeService.setTheme(theme);

    // Show notification
    this.snackBar.open(`Theme changed to ${this.getThemeDisplayName(theme.name)}`, 'Close', {
      duration: 2000,
      panelClass: ['success-snackbar']
    });

    // Force a visual update after a short delay
    setTimeout(() => {
      console.log('Theme applied, current CSS variables:', {
        primary: getComputedStyle(document.documentElement).getPropertyValue('--theme-primary'),
        accent: getComputedStyle(document.documentElement).getPropertyValue('--theme-accent'),
        background: getComputedStyle(document.documentElement).getPropertyValue('--theme-background')
      });
    }, 200);
  }

  getThemeDisplayName(themeName: string): string {
    switch (themeName) {
      case 'deep-purple-amber':
        return 'Deep Purple & Amber';
      case 'blue-orange':
        return 'Blue & Orange';
      case 'green-teal':
        return 'Green & Teal';
      case 'dark':
        return 'Dark Theme';
      case 'mystical-purple':
        return 'Mystical Purple';
      default:
        return themeName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  }

  exportTheme(): void {
    const themeJson = this.themeService.exportTheme();
    const blob = new Blob([themeJson], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${this.currentTheme.name}-theme.json`;
    link.click();
    window.URL.revokeObjectURL(url);
  }

  onFileSelected(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (this.themeService.importTheme(content)) {
          // Theme imported successfully
          console.log('Theme imported successfully');
        } else {
          console.error('Failed to import theme');
        }
      };
      reader.readAsText(file);
    }
  }
}
