import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { OracleService, OracleProfile } from '../services/oracle.service';

@Component({
  selector: 'app-oracle-profile',
  templateUrl: './oracle-profile.component.html',
  styleUrls: ['./oracle-profile.component.css']
})
export class OracleProfileComponent implements OnInit {
  oracle: OracleProfile | null = null;
  loading = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private oracleService: OracleService,
    private snackBar: MatSnackBar
  ) { }

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      const id = +params['id'];
      if (id) {
        this.loadOracleProfile(id);
      } else {
        this.error = 'Invalid oracle ID';
        this.loading = false;
      }
    });
  }

  private loadOracleProfile(id: number): void {
    this.loading = true;
    this.error = null;

    this.oracleService.getOracleProfile(id).subscribe({
      next: (response) => {
        if (response.success && response.data) {
          this.oracle = response.data;
        } else {
          this.error = response.message || 'Failed to load oracle profile';
        }
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading oracle profile:', error);
        this.error = 'Failed to load oracle profile';
        this.loading = false;
        this.snackBar.open('Failed to load oracle profile', 'Close', {
          duration: 5000,
          panelClass: ['error-snackbar']
        });
      }
    });
  }

  getFullName(): string {
    if (!this.oracle) return '';
    return `${this.oracle.firstName} ${this.oracle.lastName}`;
  }

  getExperienceText(): string {
    if (!this.oracle?.yearsOfExperience) return 'Experience not specified';
    const years = this.oracle.yearsOfExperience;
    return years === 1 ? '1 year of experience' : `${years} years of experience`;
  }

  getServicesByCategory(): { [category: string]: any[] } {
    if (!this.oracle?.services) return {};
    
    return this.oracle.services.reduce((acc, service) => {
      if (!acc[service.category]) {
        acc[service.category] = [];
      }
      acc[service.category].push(service);
      return acc;
    }, {} as { [category: string]: any[] });
  }

  getCategories(): string[] {
    return Object.keys(this.getServicesByCategory());
  }

  formatPrice(price: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  }

  formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${minutes} min`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    if (remainingMinutes === 0) {
      return `${hours}h`;
    }
    return `${hours}h ${remainingMinutes}min`;
  }

  goBack(): void {
    this.router.navigate(['/oracles']);
  }

  contactOracle(): void {
    if (this.oracle?.email) {
      window.location.href = `mailto:${this.oracle.email}`;
    }
  }

  callOracle(): void {
    if (this.oracle?.phoneNumber) {
      window.location.href = `tel:${this.oracle.phoneNumber}`;
    }
  }
}
