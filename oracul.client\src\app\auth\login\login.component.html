<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header class="login-header">
      <mat-card-title>
        <mat-icon class="login-icon">lock</mat-icon>
        Sign In to Oracul
      </mat-card-title>
      <mat-card-subtitle>
        Enter your credentials to access your account
      </mat-card-subtitle>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <!-- Email Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Email Address</mat-label>
          <input
            matInput
            type="email"
            formControlName="email"
            placeholder="Enter your email"
            autocomplete="email">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched">
            {{ getEmailErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Password Field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Password</mat-label>
          <input
            matInput
            [type]="hidePassword ? 'password' : 'text'"
            formControlName="password"
            placeholder="Enter your password"
            autocomplete="current-password">
          <button
            mat-icon-button
            matSuffix
            type="button"
            (click)="hidePassword = !hidePassword"
            [attr.aria-label]="'Hide password'"
            [attr.aria-pressed]="hidePassword">
            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched">
            {{ getPasswordErrorMessage() }}
          </mat-error>
        </mat-form-field>

        <!-- Login Button -->
        <button
          mat-raised-button
          color="primary"
          type="submit"
          class="full-width login-button"
          [disabled]="isLoading">
          <mat-icon *ngIf="isLoading">
            <mat-spinner diameter="20"></mat-spinner>
          </mat-icon>
          <mat-icon *ngIf="!isLoading">login</mat-icon>
          {{ isLoading ? 'Signing In...' : 'Sign In' }}
        </button>

        <!-- Divider -->
        <div class="divider-container">
          <mat-divider></mat-divider>
          <span class="divider-text">or</span>
          <mat-divider></mat-divider>
        </div>

        <!-- OAuth Buttons -->
        <div class="oauth-buttons">
          <!-- Google Sign-In Button -->
          <button
            mat-stroked-button
            type="button"
            class="full-width oauth-button google-button"
            [disabled]="isLoading"
            (click)="signInWithGoogle()">
            <img src="https://developers.google.com/identity/images/g-logo.png" alt="Google" class="oauth-icon">
            Continue with Google
          </button>

          <!-- Facebook Sign-In Button -->
          <button
            mat-stroked-button
            type="button"
            class="full-width oauth-button facebook-button"
            [disabled]="isLoading"
            (click)="signInWithFacebook()">
            <mat-icon class="oauth-icon facebook-icon">facebook</mat-icon>
            Continue with Facebook
          </button>
        </div>
      </form>
    </mat-card-content>

    <mat-card-actions class="login-actions">
      <div class="action-links">
        <button
          mat-button
          color="accent"
          type="button"
          (click)="navigateToForgotPassword()">
          Forgot Password?
        </button>
      </div>

      <mat-divider></mat-divider>

      <div class="register-section">
        <p class="register-text">Don't have an account?</p>
        <button
          mat-stroked-button
          color="primary"
          type="button"
          (click)="navigateToRegister()"
          class="full-width">
          <mat-icon>person_add</mat-icon>
          Create Account
        </button>
      </div>
    </mat-card-actions>
  </mat-card>
</div>
