<!-- Oracle Profile Page -->
<div class="profile-container">
  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-card class="loading-card">
      <mat-card-content>
        <div class="loading-content">
          <mat-spinner diameter="50"></mat-spinner>
          <p>Loading oracle profile...</p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-card class="error-card">
      <mat-card-content>
        <div class="error-content">
          <mat-icon color="warn">error</mat-icon>
          <h3>Error</h3>
          <p>{{ error }}</p>
          <button mat-raised-button color="primary" (click)="goBack()">
            <mat-icon>arrow_back</mat-icon>
            Go Back
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Oracle Profile Content -->
  <div *ngIf="oracle && !loading" class="profile-content">
    <!-- Header Section -->
    <mat-card class="profile-header">
      <mat-card-content>
        <div class="header-content">
          <div class="profile-image-section">
            <div class="profile-image" [class.has-image]="oracle.profilePictureUrl">
              <img
                *ngIf="oracle.profilePictureUrl"
                [src]="oracle.profilePictureUrl"
                [alt]="getFullName()"
                (error)="oracle.profilePictureUrl = undefined"
              />
              <div *ngIf="!oracle.profilePictureUrl" class="default-avatar">
                <mat-icon>person</mat-icon>
              </div>
            </div>
            <div class="availability-badge" [class.available]="oracle.isAvailable">
              <mat-icon>{{ oracle.isAvailable ? 'check_circle' : 'schedule' }}</mat-icon>
              {{ oracle.isAvailable ? 'Available' : 'Busy' }}
            </div>
          </div>
          
          <div class="profile-info">
            <h1>{{ getFullName() }}</h1>
            <p class="experience" *ngIf="oracle.yearsOfExperience">
              <mat-icon>star</mat-icon>
              {{ getExperienceText() }}
            </p>
            <p class="hourly-rate" *ngIf="oracle.hourlyRate">
              <mat-icon>attach_money</mat-icon>
              {{ formatPrice(oracle.hourlyRate) }} per hour
            </p>
            
            <!-- Contact Actions -->
            <div class="contact-actions">
              <button mat-raised-button color="primary" (click)="contactOracle()" *ngIf="oracle.email">
                <mat-icon>email</mat-icon>
                Contact
              </button>
              <button mat-stroked-button (click)="callOracle()" *ngIf="oracle.phoneNumber">
                <mat-icon>phone</mat-icon>
                Call
              </button>
              <button mat-stroked-button (click)="goBack()">
                <mat-icon>arrow_back</mat-icon>
                Back
              </button>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- About Section -->
    <mat-card class="about-section" *ngIf="oracle.about">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>person</mat-icon>
          About
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p class="about-text">{{ oracle.about }}</p>
      </mat-card-content>
    </mat-card>

    <!-- Specializations Section -->
    <mat-card class="specializations-section" *ngIf="oracle.specializations.length > 0">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>psychology</mat-icon>
          Specializations
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="chips-container">
          <mat-chip-set>
            <mat-chip *ngFor="let specialization of oracle.specializations">
              {{ specialization }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Languages Section -->
    <mat-card class="languages-section" *ngIf="oracle.languages.length > 0">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>language</mat-icon>
          Languages
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="chips-container">
          <mat-chip-set>
            <mat-chip *ngFor="let language of oracle.languages">
              {{ language }}
            </mat-chip>
          </mat-chip-set>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Services Section -->
    <mat-card class="services-section" *ngIf="oracle.services.length > 0">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>room_service</mat-icon>
          Services & Pricing
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div *ngFor="let category of getCategories()" class="service-category">
          <h3 class="category-title">{{ category }}</h3>
          <div class="services-grid">
            <mat-card *ngFor="let service of getServicesByCategory()[category]" class="service-card">
              <mat-card-header>
                <mat-card-title>{{ service.name }}</mat-card-title>
                <mat-card-subtitle>{{ formatDuration(service.durationMinutes) }}</mat-card-subtitle>
              </mat-card-header>
              <mat-card-content>
                <p *ngIf="service.description" class="service-description">{{ service.description }}</p>
                <p *ngIf="service.notes" class="service-notes">
                  <mat-icon>info</mat-icon>
                  {{ service.notes }}
                </p>
                <div class="service-price">
                  <span class="price">{{ formatPrice(service.price) }}</span>
                </div>
              </mat-card-content>
              <mat-card-actions>
                <button mat-raised-button color="accent">
                  <mat-icon>shopping_cart</mat-icon>
                  Book Service
                </button>
              </mat-card-actions>
            </mat-card>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- No Services Message -->
    <mat-card class="no-services-section" *ngIf="oracle.services.length === 0">
      <mat-card-content>
        <div class="no-services-content">
          <mat-icon>info</mat-icon>
          <h3>No Services Available</h3>
          <p>This oracle hasn't added any services yet.</p>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
