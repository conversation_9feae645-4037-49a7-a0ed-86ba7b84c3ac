using System.ComponentModel.DataAnnotations;

namespace Oracul.Data.Models
{
    /// <summary>
    /// Service entity representing services offered by oracles
    /// </summary>
    public class Service : BaseEntity
    {
        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        [Required]
        public decimal Price { get; set; }

        [Required]
        public int DurationMinutes { get; set; }

        public bool IsActive { get; set; } = true;

        [MaxLength(50)]
        public string Category { get; set; } = string.Empty;

        // Navigation properties
        public virtual ICollection<OracleService> OracleServices { get; set; } = new List<OracleService>();
    }
}
