using Microsoft.EntityFrameworkCore;
using Oracul.Data.Data;
using Oracul.Data.Interfaces;
using Oracul.Data.Models;

namespace Oracul.Data.Repositories
{
    /// <summary>
    /// Repository implementation for OracleService entity operations
    /// </summary>
    public class OracleServiceRepository : Repository<OracleService>, IOracleServiceRepository
    {
        public OracleServiceRepository(OraculDbContext context) : base(context)
        {
        }

        /// <summary>
        /// Get services offered by a specific oracle
        /// </summary>
        public async Task<IEnumerable<OracleService>> GetServicesByOracleIdAsync(int oracleId)
        {
            return await _context.OracleServices
                .Include(os => os.Service)
                .Where(os => os.UserId == oracleId && os.IsActive && os.Service.IsActive)
                .OrderBy(os => os.Service.Category)
                .ThenBy(os => os.Service.Name)
                .ToListAsync();
        }

        /// <summary>
        /// Get oracles offering a specific service
        /// </summary>
        public async Task<IEnumerable<OracleService>> GetOraclesByServiceIdAsync(int serviceId)
        {
            return await _context.OracleServices
                .Include(os => os.User)
                .Where(os => os.ServiceId == serviceId && os.IsActive && os.User.IsOracle && os.User.IsAvailable)
                .OrderBy(os => os.CustomPrice ?? os.Service.Price)
                .ToListAsync();
        }

        /// <summary>
        /// Get oracle service with full details (including User and Service)
        /// </summary>
        public async Task<OracleService?> GetOracleServiceWithDetailsAsync(int oracleId, int serviceId)
        {
            return await _context.OracleServices
                .Include(os => os.User)
                .Include(os => os.Service)
                .FirstOrDefaultAsync(os => os.UserId == oracleId && os.ServiceId == serviceId);
        }
    }
}
