# Oracle Profile Feature

This document describes the Oracle Profile feature implementation for the Oracul application.

## Overview

The Oracle Profile feature allows users to register as service providers (oracles) and showcase their services, expertise, and pricing to potential clients. The feature includes both backend API endpoints and frontend Angular components.

## Backend Implementation

### Database Models

#### User Entity Extensions
- Added oracle-specific fields to the existing `User` entity:
  - `About`: Text description of the oracle
  - `IsOracle`: Boolean flag to identify oracle users
  - `HourlyRate`: Optional hourly rate for services
  - `Specializations`: JSON array of specialization areas
  - `YearsOfExperience`: Years of experience in the field
  - `Languages`: JSON array of supported languages
  - `IsAvailable`: Availability status

#### New Entities
1. **Service**: Represents services that can be offered
   - Name, Description, Price, Duration, Category
   - Base template for services

2. **OracleService**: Junction table for Oracle-Service relationships
   - Links oracles to services they offer
   - Allows custom pricing and duration overrides
   - Includes oracle-specific notes

### API Endpoints

#### OracleController (`/api/oracle`)
- `GET /` - Get all oracle profiles
- `GET /{id}` - Get specific oracle profile with services
- `GET /available` - Get only available oracles
- `PUT /{id}` - Update oracle profile (authenticated)

### Repositories
- `IServiceRepository` / `ServiceRepository`
- `IOracleServiceRepository` / `OracleServiceRepository`
- Extended `IUserRepository` with oracle-specific methods

## Frontend Implementation

### Components

#### OracleListComponent (`/oracles`)
- Displays grid of all oracle profiles
- Search functionality (name, specializations, services)
- Filter by availability
- Responsive card layout with Material Design

#### OracleProfileComponent (`/oracle/:id`)
- Detailed oracle profile view
- Profile picture, about section, experience
- Specializations and languages as chips
- Services organized by category with pricing
- Contact actions (email, phone)

### Services
- `OracleService`: Angular service for API communication
- Type-safe interfaces for all data models

### Navigation
- Updated app toolbar with oracle navigation
- Public access (no authentication required for viewing)
- Responsive design for mobile devices

## Features

### Oracle Profile Display
- **Profile Header**: Photo, name, experience, hourly rate, availability
- **About Section**: Detailed description of the oracle
- **Specializations**: Categorized expertise areas
- **Languages**: Supported communication languages
- **Services**: Organized by category with custom pricing

### Search & Filtering
- Search by oracle name, specializations, or services
- Filter by availability status
- Real-time filtering with immediate results

### Responsive Design
- Mobile-first approach
- Adaptive layouts for different screen sizes
- Touch-friendly interface elements

## Database Migration

To apply the database changes:

```bash
# Create migration
.\migrate-clean.ps1 -Action add -MigrationName "AddOracleFeatures"

# Apply migration
.\migrate-clean.ps1 -Action update
```

## Seed Data

The system includes default services:
- Tarot Reading ($50, 30 min)
- Astrology Consultation ($75, 45 min)
- Palm Reading ($40, 25 min)
- Crystal Healing ($60, 60 min)
- Spiritual Guidance ($80, 50 min)

## Usage

### For Oracles (Service Providers)
1. Register/login to the system
2. Update profile with oracle-specific information
3. Add services and custom pricing
4. Set availability status

### For Clients
1. Browse oracle directory at `/oracles`
2. Search for specific specializations or services
3. View detailed oracle profiles
4. Contact oracles directly via email/phone

## Technical Notes

### Material Design
- Follows existing design patterns
- Uses Angular Material components
- Consistent color scheme and typography

### Data Storage
- Specializations and languages stored as JSON arrays
- Flexible schema for future extensions
- Soft delete support for all entities

### Performance
- Lazy loading of oracle services
- Efficient filtering and search
- Optimized database queries with includes

## Future Enhancements

Potential improvements:
- Booking system integration
- Review and rating system
- Oracle dashboard for managing services
- Payment processing integration
- Advanced search filters (price range, location)
- Oracle verification system
