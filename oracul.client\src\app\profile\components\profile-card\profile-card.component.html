<mat-card class="profile-card" [class.compact]="compact" (click)="onViewProfile()">
  <!-- Profile Header -->
  <mat-card-header class="profile-header">
    <div mat-card-avatar class="profile-avatar">
      <img 
        [src]="profile.profilePhotoUrl || '/assets/images/default-avatar.png'" 
        [alt]="fullName"
        class="avatar-image"
        (error)="$event.target.src='/assets/images/default-avatar.png'">
    </div>
    
    <mat-card-title class="profile-name">{{ fullName }}</mat-card-title>
    
    <mat-card-subtitle class="profile-details">
      <div class="title-location">
        <span class="professional-title" *ngIf="profile.professionalTitle">
          {{ profile.professionalTitle }}
        </span>
        <span class="location" *ngIf="displayLocation">
          <mat-icon class="location-icon">location_on</mat-icon>
          {{ displayLocation }}
        </span>
      </div>
      
      <div class="experience-info" *ngIf="experienceYears > 0">
        <mat-icon class="experience-icon">work</mat-icon>
        <span>{{ experienceYears }} year{{ experienceYears !== 1 ? 's' : '' }} experience</span>
      </div>
    </mat-card-subtitle>
  </mat-card-header>

  <mat-card-content class="profile-content">
    <!-- About Section -->
    <div class="about-section" *ngIf="profile.summary">
      <h4 class="section-title">
        <mat-icon>person</mat-icon>
        About
      </h4>
      <p class="about-text">
        {{ truncateText(profile.summary, compact ? 120 : 200) }}
      </p>
    </div>

    <!-- Skills & Expertise Section -->
    <div class="skills-section" *ngIf="topSkills.length > 0">
      <h4 class="section-title">
        <mat-icon>psychology</mat-icon>
        Skills & Expertise
      </h4>
      <div class="skills-container">
        <mat-chip-set class="skills-chips">
          <mat-chip *ngFor="let skill of topSkills" class="skill-chip">
            {{ skill }}
          </mat-chip>
          <mat-chip 
            *ngIf="profile.skills.length > topSkills.length" 
            class="more-skills-chip">
            +{{ profile.skills.length - topSkills.length }} more
          </mat-chip>
        </mat-chip-set>
      </div>
    </div>

    <!-- Contact Information Section -->
    <div class="contact-section" *ngIf="hasContactInfo">
      <h4 class="section-title">
        <mat-icon>contact_mail</mat-icon>
        Contact Information
      </h4>
      <div class="contact-info">
        <div class="contact-item" *ngIf="primaryEmail">
          <button 
            mat-icon-button 
            class="contact-button email-button"
            (click)="onEmailClick($event)"
            matTooltip="Send Email">
            <mat-icon>email</mat-icon>
          </button>
          <span class="contact-text">{{ primaryEmail }}</span>
        </div>
        
        <div class="contact-item" *ngIf="primaryPhone">
          <button 
            mat-icon-button 
            class="contact-button phone-button"
            (click)="onPhoneClick($event)"
            matTooltip="Call">
            <mat-icon>phone</mat-icon>
          </button>
          <span class="contact-text">{{ formatPhoneNumber(primaryPhone) }}</span>
        </div>
        
        <div class="contact-item" *ngIf="profile.contactInfo?.website">
          <button 
            mat-icon-button 
            class="contact-button website-button"
            (click)="onWebsiteClick($event)"
            matTooltip="Visit Website">
            <mat-icon>language</mat-icon>
          </button>
          <span class="contact-text">Website</span>
        </div>
      </div>
    </div>

    <!-- Profile Stats -->
    <div class="stats-section" *ngIf="!compact">
      <div class="stat-item">
        <mat-icon>visibility</mat-icon>
        <span>{{ profile.profileViews }} views</span>
      </div>
      <div class="stat-item" *ngIf="profile.skills.length > 0">
        <mat-icon>thumb_up</mat-icon>
        <span>{{ getTotalEndorsements() }} endorsements</span>
      </div>
      <div class="stat-item" *ngIf="profile.portfolioItems?.length > 0">
        <mat-icon>work_outline</mat-icon>
        <span>{{ profile.portfolioItems.length }} project{{ profile.portfolioItems.length !== 1 ? 's' : '' }}</span>
      </div>
    </div>
  </mat-card-content>

  <!-- Card Actions -->
  <mat-card-actions class="profile-actions">
    <button 
      mat-raised-button 
      color="primary" 
      class="view-profile-btn"
      *ngIf="showViewProfileButton"
      (click)="onViewProfile()">
      <mat-icon>visibility</mat-icon>
      View Profile
    </button>
    
    <button 
      mat-stroked-button 
      color="accent" 
      class="contact-btn"
      *ngIf="showContactButton && hasContactInfo"
      (click)="onContact()">
      <mat-icon>contact_mail</mat-icon>
      Contact
    </button>
    
    <button 
      mat-icon-button 
      class="share-btn"
      matTooltip="Share Profile">
      <mat-icon>share</mat-icon>
    </button>
  </mat-card-actions>

  <!-- Profile Completion Indicator (for own profile) -->
  <div class="completion-indicator" *ngIf="profile.profileCompletionPercentage < 100">
    <mat-progress-bar 
      [value]="profile.profileCompletionPercentage" 
      [color]="profileCompletionColor"
      class="completion-bar">
    </mat-progress-bar>
    <span class="completion-text">{{ profile.profileCompletionPercentage }}% complete</span>
  </div>
</mat-card>
