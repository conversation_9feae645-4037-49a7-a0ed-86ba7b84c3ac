using System.ComponentModel.DataAnnotations;

namespace Oracul.Server.Models
{
    /// <summary>
    /// Oracle profile information for display
    /// </summary>
    public class OracleProfileDto
    {
        public int Id { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? ProfilePictureUrl { get; set; }
        public string? About { get; set; }
        public decimal? HourlyRate { get; set; }
        public List<string> Specializations { get; set; } = new();
        public int? YearsOfExperience { get; set; }
        public List<string> Languages { get; set; } = new();
        public bool IsAvailable { get; set; }
        public List<OracleServiceDto> Services { get; set; } = new();
        public DateTime? LastLoginAt { get; set; }
    }

    /// <summary>
    /// Service offered by an oracle
    /// </summary>
    public class OracleServiceDto
    {
        public int ServiceId { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public decimal Price { get; set; }
        public int DurationMinutes { get; set; }
        public string Category { get; set; } = string.Empty;
        public string? Notes { get; set; }
    }

    /// <summary>
    /// Service information
    /// </summary>
    public class ServiceDto
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public decimal Price { get; set; }
        public int DurationMinutes { get; set; }
        public string Category { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }

    /// <summary>
    /// Request to update oracle profile
    /// </summary>
    public class UpdateOracleProfileRequest
    {
        [MaxLength(2000)]
        public string? About { get; set; }

        public decimal? HourlyRate { get; set; }

        public List<string> Specializations { get; set; } = new();

        public int? YearsOfExperience { get; set; }

        public List<string> Languages { get; set; } = new();

        public bool IsAvailable { get; set; } = true;
    }

    /// <summary>
    /// Request to add/update oracle service
    /// </summary>
    public class OracleServiceRequest
    {
        [Required]
        public int ServiceId { get; set; }

        public decimal? CustomPrice { get; set; }

        public int? CustomDurationMinutes { get; set; }

        [MaxLength(500)]
        public string? Notes { get; set; }

        public bool IsActive { get; set; } = true;
    }
}
